"""
Test script for ImageNet100 data loader.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.data_loader import ImageNet100DataLoader
from src.utils import setup_logging
from configs.config import Config
import torch

def main():
    """Test the data loader functionality."""
    
    # Set up logging
    logger = setup_logging()
    
    # Set random seeds for reproducibility
    Config.set_random_seeds()
    
    try:
        # Initialize data loader
        logger.info("Initializing ImageNet100 data loader...")
        data_loader_manager = ImageNet100DataLoader(
            root_dir=Config.IMAGENET100_PATH,
            batch_size=Config.BATCH_SIZE,
            num_workers=Config.NUM_WORKERS,
            pin_memory=Config.PIN_MEMORY,
            validation_split=Config.VALIDATION_SPLIT
        )
        
        # Get data loaders
        logger.info("Setting up data loaders...")
        train_loader, val_loader, test_loader = data_loader_manager.get_data_loaders()
        
        # Get dataset information
        dataset_info = data_loader_manager.get_dataset_info()
        
        # Print dataset information
        logger.info("Dataset Information:")
        logger.info(f"  Number of classes: {dataset_info['num_classes']}")
        logger.info(f"  Training samples: {dataset_info['train_samples']}")
        logger.info(f"  Validation samples: {dataset_info['val_samples']}")
        logger.info(f"  Test samples: {dataset_info['test_samples']}")
        logger.info(f"  Batch size: {dataset_info['batch_size']}")
        
        # Test loading a batch from each loader
        if train_loader:
            logger.info("Testing training data loader...")
            train_batch = next(iter(train_loader))
            images, labels = train_batch
            logger.info(f"  Training batch shape: {images.shape}")
            logger.info(f"  Training labels shape: {labels.shape}")
            logger.info(f"  Training batch min/max: {images.min():.3f}/{images.max():.3f}")
        
        if val_loader:
            logger.info("Testing validation data loader...")
            val_batch = next(iter(val_loader))
            images, labels = val_batch
            logger.info(f"  Validation batch shape: {images.shape}")
            logger.info(f"  Validation labels shape: {labels.shape}")
            logger.info(f"  Validation batch min/max: {images.min():.3f}/{images.max():.3f}")
        
        if test_loader:
            logger.info("Testing test data loader...")
            test_batch = next(iter(test_loader))
            images, labels = test_batch
            logger.info(f"  Test batch shape: {images.shape}")
            logger.info(f"  Test labels shape: {labels.shape}")
            logger.info(f"  Test batch min/max: {images.min():.3f}/{images.max():.3f}")
        
        logger.info("Data loader test completed successfully!")
        
    except FileNotFoundError as e:
        logger.error(f"Dataset not found: {e}")
        logger.info("Please ensure the ImageNet100 dataset is available at the specified path.")
        logger.info(f"Expected path: {Config.IMAGENET100_PATH}")
        
    except Exception as e:
        logger.error(f"Error during data loader test: {str(e)}")
        raise

if __name__ == "__main__":
    main()
