"""
ResNet-50 Training Module

This module provides comprehensive training functionality including:
- Training loop with progress monitoring
- Validation during training
- Checkpoint saving and loading
- Learning rate scheduling
- Early stopping
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import time
import logging
import json
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from tqdm import tqdm

from .utils import AverageMeter, calculate_accuracy, save_checkpoint, load_checkpoint, format_time

logger = logging.getLogger(__name__)


class ResNet50Trainer:
    """Comprehensive trainer for ResNet-50 model."""
    
    def __init__(self, model: nn.Module, device: torch.device, config: object):
        """
        Initialize the trainer.
        
        Args:
            model (nn.Module): ResNet-50 model to train
            device (torch.device): Device to train on (CPU/GPU)
            config (object): Configuration object with training parameters
        """
        self.model = model.to(device)
        self.device = device
        self.config = config
        
        # Training state
        self.current_epoch = 0
        self.best_accuracy = 0.0
        self.training_history = {
            'train_loss': [],
            'train_acc': [],
            'val_loss': [],
            'val_acc': [],
            'learning_rates': []
        }
        
        # Initialize optimizer, scheduler, and loss function
        self.optimizer = None
        self.scheduler = None
        self.criterion = None
        
        self._setup_training_components()
        
        logger.info(f"Trainer initialized on device: {device}")
    
    def _setup_training_components(self):
        """Set up optimizer, scheduler, and loss function."""
        # Loss function
        self.criterion = nn.CrossEntropyLoss()
        
        # Optimizer
        if self.config.OPTIMIZER == "SGD":
            self.optimizer = optim.SGD(
                self.model.parameters(),
                lr=self.config.LEARNING_RATE,
                momentum=self.config.MOMENTUM,
                weight_decay=self.config.WEIGHT_DECAY
            )
        elif self.config.OPTIMIZER == "Adam":
            self.optimizer = optim.Adam(
                self.model.parameters(),
                lr=self.config.LEARNING_RATE,
                weight_decay=self.config.WEIGHT_DECAY
            )
        elif self.config.OPTIMIZER == "AdamW":
            self.optimizer = optim.AdamW(
                self.model.parameters(),
                lr=self.config.LEARNING_RATE,
                weight_decay=self.config.WEIGHT_DECAY
            )
        else:
            raise ValueError(f"Unsupported optimizer: {self.config.OPTIMIZER}")
        
        # Learning rate scheduler
        if self.config.SCHEDULER == "StepLR":
            self.scheduler = optim.lr_scheduler.StepLR(
                self.optimizer,
                step_size=self.config.STEP_SIZE,
                gamma=self.config.GAMMA
            )
        elif self.config.SCHEDULER == "CosineAnnealingLR":
            self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer,
                T_max=self.config.NUM_EPOCHS
            )
        elif self.config.SCHEDULER == "ReduceLROnPlateau":
            self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
                self.optimizer,
                mode='max',
                factor=self.config.GAMMA,
                patience=5,
                verbose=True
            )
        else:
            raise ValueError(f"Unsupported scheduler: {self.config.SCHEDULER}")
        
        logger.info(f"Training components initialized:")
        logger.info(f"  Optimizer: {self.config.OPTIMIZER}")
        logger.info(f"  Scheduler: {self.config.SCHEDULER}")
        logger.info(f"  Learning Rate: {self.config.LEARNING_RATE}")
    
    def train_epoch(self, train_loader: DataLoader) -> Tuple[float, float]:
        """
        Train for one epoch.
        
        Args:
            train_loader (DataLoader): Training data loader
            
        Returns:
            Tuple[float, float]: (average_loss, average_accuracy)
        """
        self.model.train()
        
        # Metrics tracking
        losses = AverageMeter('Loss', ':.4f')
        top1 = AverageMeter('Acc@1', ':6.2f')
        
        # Progress bar
        pbar = tqdm(train_loader, desc=f'Epoch {self.current_epoch+1}/{self.config.NUM_EPOCHS}')
        
        for batch_idx, (images, targets) in enumerate(pbar):
            # Move data to device
            images = images.to(self.device, non_blocking=True)
            targets = targets.to(self.device, non_blocking=True)
            
            # Forward pass
            outputs = self.model(images)
            loss = self.criterion(outputs, targets)
            
            # Backward pass
            self.optimizer.zero_grad()
            loss.backward()
            self.optimizer.step()
            
            # Calculate accuracy
            acc1 = calculate_accuracy(outputs, targets, topk=(1,))[0]
            
            # Update metrics
            losses.update(loss.item(), images.size(0))
            top1.update(acc1.item(), images.size(0))
            
            # Update progress bar
            if batch_idx % self.config.LOG_INTERVAL == 0:
                pbar.set_postfix({
                    'Loss': f'{losses.avg:.4f}',
                    'Acc': f'{top1.avg:.2f}%',
                    'LR': f'{self.optimizer.param_groups[0]["lr"]:.6f}'
                })
        
        return losses.avg, top1.avg
    
    def validate(self, val_loader: DataLoader) -> Tuple[float, float]:
        """
        Validate the model.
        
        Args:
            val_loader (DataLoader): Validation data loader
            
        Returns:
            Tuple[float, float]: (average_loss, average_accuracy)
        """
        self.model.eval()
        
        losses = AverageMeter('Loss', ':.4f')
        top1 = AverageMeter('Acc@1', ':6.2f')
        
        with torch.no_grad():
            for images, targets in tqdm(val_loader, desc='Validation'):
                images = images.to(self.device, non_blocking=True)
                targets = targets.to(self.device, non_blocking=True)
                
                # Forward pass
                outputs = self.model(images)
                loss = self.criterion(outputs, targets)
                
                # Calculate accuracy
                acc1 = calculate_accuracy(outputs, targets, topk=(1,))[0]
                
                # Update metrics
                losses.update(loss.item(), images.size(0))
                top1.update(acc1.item(), images.size(0))
        
        return losses.avg, top1.avg
    
    def train(self, train_loader: DataLoader, val_loader: Optional[DataLoader] = None) -> Dict:
        """
        Complete training loop.
        
        Args:
            train_loader (DataLoader): Training data loader
            val_loader (DataLoader, optional): Validation data loader
            
        Returns:
            Dict: Training history and results
        """
        logger.info("Starting training...")
        logger.info(f"Training for {self.config.NUM_EPOCHS} epochs")
        logger.info(f"Training samples: {len(train_loader.dataset)}")
        if val_loader:
            logger.info(f"Validation samples: {len(val_loader.dataset)}")
        
        start_time = time.time()
        epochs_without_improvement = 0
        
        for epoch in range(self.config.NUM_EPOCHS):
            self.current_epoch = epoch
            epoch_start_time = time.time()
            
            # Training phase
            train_loss, train_acc = self.train_epoch(train_loader)
            
            # Validation phase
            val_loss, val_acc = 0.0, 0.0
            if val_loader:
                val_loss, val_acc = self.validate(val_loader)
            
            # Update learning rate scheduler
            if self.config.SCHEDULER == "ReduceLROnPlateau":
                self.scheduler.step(val_acc if val_loader else train_acc)
            else:
                self.scheduler.step()
            
            # Record history
            self.training_history['train_loss'].append(train_loss)
            self.training_history['train_acc'].append(train_acc)
            self.training_history['val_loss'].append(val_loss)
            self.training_history['val_acc'].append(val_acc)
            self.training_history['learning_rates'].append(self.optimizer.param_groups[0]['lr'])
            
            # Calculate epoch time
            epoch_time = time.time() - epoch_start_time
            
            # Log epoch results
            logger.info(f"Epoch {epoch+1}/{self.config.NUM_EPOCHS} completed in {format_time(epoch_time)}")
            logger.info(f"  Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}%")
            if val_loader:
                logger.info(f"  Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.2f}%")
            logger.info(f"  Learning Rate: {self.optimizer.param_groups[0]['lr']:.6f}")
            
            # Check for best model
            current_acc = val_acc if val_loader else train_acc
            if current_acc > self.best_accuracy:
                self.best_accuracy = current_acc
                epochs_without_improvement = 0
                
                # Save best model
                best_model_path = Path(self.config.CHECKPOINTS_DIR) / "best_model.pth"
                save_checkpoint(
                    self.model, self.optimizer, epoch, 
                    val_loss if val_loader else train_loss, 
                    current_acc, str(best_model_path)
                )
                logger.info(f"New best model saved with accuracy: {current_acc:.2f}%")
            else:
                epochs_without_improvement += 1
            
            # Save regular checkpoint
            if (epoch + 1) % self.config.SAVE_INTERVAL == 0:
                checkpoint_path = Path(self.config.CHECKPOINTS_DIR) / f"checkpoint_epoch_{epoch+1}.pth"
                save_checkpoint(
                    self.model, self.optimizer, epoch,
                    val_loss if val_loader else train_loss,
                    current_acc, str(checkpoint_path)
                )
                logger.info(f"Checkpoint saved: {checkpoint_path}")
            
            # Early stopping
            if epochs_without_improvement >= self.config.EARLY_STOPPING_PATIENCE:
                logger.info(f"Early stopping triggered after {epochs_without_improvement} epochs without improvement")
                break
        
        # Training completed
        total_time = time.time() - start_time
        logger.info(f"Training completed in {format_time(total_time)}")
        logger.info(f"Best validation accuracy: {self.best_accuracy:.2f}%")
        
        # Save final model
        final_model_path = Path(self.config.FINAL_MODEL_PATH)
        save_checkpoint(
            self.model, self.optimizer, self.current_epoch,
            val_loss if val_loader else train_loss,
            current_acc, str(final_model_path)
        )
        logger.info(f"Final model saved: {final_model_path}")
        
        # Save training history
        history_path = Path(self.config.RESULTS_DIR) / "training_history.json"
        with open(history_path, 'w') as f:
            json.dump(self.training_history, f, indent=2)
        logger.info(f"Training history saved: {history_path}")
        
        return {
            'best_accuracy': self.best_accuracy,
            'total_epochs': self.current_epoch + 1,
            'total_time': total_time,
            'training_history': self.training_history
        }
