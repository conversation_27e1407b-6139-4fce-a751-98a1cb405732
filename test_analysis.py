"""
Test script for ResNet-50 model analysis.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.model_analysis import ResNet50Analyzer
from src.utils import setup_logging, create_directories, print_system_info
from configs.config import Config

def main():
    """Test the model analysis functionality."""
    
    # Print system information
    print_system_info()
    
    # Set up logging
    logger = setup_logging()
    
    # Create necessary directories
    Config.create_directories()
    
    # Set random seeds for reproducibility
    Config.set_random_seeds()
    
    # Print configuration
    Config.print_config()
    
    try:
        # Initialize analyzer
        logger.info("Initializing ResNet-50 analyzer...")
        analyzer = ResNet50Analyzer(
            pretrained=Config.PRETRAINED,
            num_classes=Config.NUM_CLASSES
        )
        
        # Perform complete analysis
        logger.info("Starting complete model analysis...")
        analysis_results = analyzer.perform_complete_analysis(
            input_size=Config.INPUT_SIZE
        )
        
        # Print summary
        analyzer.print_summary()
        
        # Export results
        logger.info("Exporting analysis results...")
        analyzer.export_to_json(Config.MODEL_ANALYSIS_JSON)
        analyzer.export_to_csv(Config.MODEL_ANALYSIS_CSV)
        
        logger.info("Model analysis completed successfully!")
        logger.info(f"Results saved to:")
        logger.info(f"  JSON: {Config.MODEL_ANALYSIS_JSON}")
        logger.info(f"  CSV: {Config.MODEL_ANALYSIS_CSV}")
        
    except Exception as e:
        logger.error(f"Error during model analysis: {str(e)}")
        raise

if __name__ == "__main__":
    main()
