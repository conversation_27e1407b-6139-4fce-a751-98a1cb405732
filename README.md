# ResNet-50 Analysis and Training Pipeline

A comprehensive implementation of ResNet-50 model analysis, training, and evaluation pipeline for ImageNet100 dataset.

## 🚀 Features

### Phase 1: Model Architecture Analysis
- **Detailed Layer Analysis**: Layer-by-layer breakdown with input/output dimensions, parameters, and types
- **FLOP Computation**: Floating-point operations calculation using THOP library
- **Memory Analysis**: Comprehensive memory usage estimation
- **Export Formats**: Results exported to both JSON and CSV formats
- **Summary Statistics**: Total parameters, model size, and computational requirements

### Phase 2: Training Implementation
- **ImageNet100 Support**: Custom data loader with proper preprocessing and augmentation
- **Training Monitoring**: Real-time progress tracking with loss and accuracy metrics
- **Validation Support**: Automatic train/validation split with monitoring
- **Checkpoint System**: Automatic saving of best models and regular checkpoints
- **Learning Rate Scheduling**: Support for StepLR, CosineAnnealingLR, and ReduceLROnPlateau
- **Early Stopping**: Configurable early stopping to prevent overfitting

### Phase 3: Evaluation and Results
- **Comprehensive Metrics**: Top-1 and Top-5 accuracy calculation
- **Confusion Matrix**: Normalized confusion matrix with visualization
- **Per-Class Analysis**: Detailed per-class accuracy, precision, recall, and F1-score
- **Visualization**: Automatic generation of plots and charts
- **Results Export**: JSON and CSV export of all evaluation metrics

## 📁 Project Structure

```
new_model/
├── src/                          # Source code modules
│   ├── __init__.py
│   ├── model_analysis.py         # Phase 1: Model architecture analysis
│   ├── data_loader.py            # ImageNet100 data loading and preprocessing
│   ├── trainer.py                # Phase 2: Training pipeline
│   ├── evaluator.py              # Phase 3: Evaluation and metrics
│   └── utils.py                  # Common utilities and helper functions
├── configs/                      # Configuration files
│   ├── __init__.py
│   └── config.py                 # Main configuration parameters
├── results/                      # Analysis and evaluation results
│   ├── resnet50_analysis.json    # Detailed model analysis (JSON)
│   ├── resnet50_analysis.csv     # Layer-by-layer analysis (CSV)
│   ├── training_history.json     # Training progress history
│   ├── evaluation_results.json   # Comprehensive evaluation results
│   ├── per_class_results.csv     # Per-class metrics
│   ├── confusion_matrix.png      # Confusion matrix visualization
│   └── per_class_accuracy.png    # Per-class accuracy chart
├── checkpoints/                  # Model checkpoints
│   ├── best_model.pth            # Best performing model
│   └── resnet50_final.pth        # Final trained model
├── logs/                         # Training and execution logs
├── requirements.txt              # Python dependencies
├── main.py                       # Main pipeline orchestrator
├── test_analysis.py              # Model analysis testing script
├── test_dataloader.py            # Data loader testing script
└── README.md                     # This file
```

## 🛠️ Installation

1. **Clone or download the project files**

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Verify installation**:
   ```bash
   python test_analysis.py
   ```

## 📊 Model Analysis Results

The ResNet-50 analysis reveals:

- **Total Parameters**: 23,712,932 (23.7M)
- **Trainable Parameters**: 23,712,932 (100%)
- **Model Size**: 90.46 MB
- **Total FLOPs**: 4.132G
- **Memory Usage**: 212.88 MB
- **Layers Analyzed**: 158 individual layers

### Key Architecture Insights:
- Modified final layer for 100 classes (ImageNet100)
- Comprehensive layer-by-layer breakdown available in CSV format
- Detailed FLOP analysis for computational efficiency assessment
- Memory usage estimation for deployment planning

## 🚀 Usage

### Run Complete Pipeline
```bash
python main.py --phase all
```

### Run Individual Phases
```bash
# Phase 1: Model Analysis Only
python main.py --phase analysis

# Phase 2: Training Only
python main.py --phase train

# Phase 3: Evaluation Only
python main.py --phase evaluate
```

### Customize Training Parameters
```bash
# Train with custom epochs and batch size
python main.py --phase train --epochs 10 --batch-size 16

# Skip analysis and train directly
python main.py --phase all --skip-analysis

# Skip training and evaluate existing model
python main.py --phase all --skip-training
```

## ⚙️ Configuration

Key configuration parameters in `configs/config.py`:

### Dataset Configuration
- **Dataset Path**: `C:\Users\<USER>\Desktop\new\imagenet100`
- **Batch Size**: 32
- **Validation Split**: 20%
- **Image Size**: 224x224
- **Data Augmentation**: Enabled (horizontal flip, rotation, color jitter)

### Training Configuration
- **Learning Rate**: 0.001
- **Optimizer**: SGD with momentum (0.9)
- **Weight Decay**: 1e-4
- **Scheduler**: StepLR (step_size=15, gamma=0.1)
- **Epochs**: 50
- **Early Stopping**: 10 epochs patience

### Model Configuration
- **Architecture**: ResNet-50
- **Pretrained**: ImageNet weights
- **Classes**: 100 (ImageNet100)
- **Input Size**: (1, 3, 224, 224)

## 📈 Results Summary

### Training Performance
- **Best Validation Accuracy**: 5.00% (limited by small dataset)
- **Training Time**: ~45 seconds for 2 epochs (CPU)
- **Convergence**: Early stopping implemented for optimal performance

### Evaluation Metrics
- **Top-1 Accuracy**: 0.98%
- **Top-5 Accuracy**: 5.88%
- **Test Loss**: 4.6133
- **Per-Class Analysis**: Available with detailed statistics

*Note: Low accuracy is expected due to the very small dataset size (only 102 samples total)*

## 🔧 Technical Implementation

### Key Features
- **Reproducibility**: Fixed random seeds for consistent results
- **Error Handling**: Comprehensive error handling and logging
- **Memory Efficiency**: Optimized data loading with pin_memory and num_workers
- **Checkpoint System**: Automatic saving of best models and regular checkpoints
- **Progress Monitoring**: Real-time training progress with tqdm progress bars
- **Visualization**: Automatic generation of confusion matrix and accuracy plots

### Dependencies
- **PyTorch**: 2.4.1+ (deep learning framework)
- **torchvision**: Model and data utilities
- **torchinfo**: Model analysis and summary
- **thop**: FLOP calculation
- **pandas**: Data manipulation and CSV export
- **matplotlib/seaborn**: Visualization and plotting
- **scikit-learn**: Evaluation metrics
- **tqdm**: Progress bars
- **PIL**: Image processing

## 🎯 Use Cases

This pipeline is ideal for:
- **Research**: Model architecture analysis and comparison
- **Education**: Understanding ResNet-50 architecture in detail
- **Prototyping**: Quick setup for image classification experiments
- **Benchmarking**: Standardized evaluation metrics and reporting
- **Production**: Checkpoint system for model deployment

## 📝 Notes

- The current dataset appears to be a very small subset of ImageNet100
- For production use, ensure you have the complete ImageNet100 dataset
- Training performance will significantly improve with GPU acceleration
- Adjust batch size and learning rate based on your hardware capabilities

## 🤝 Contributing

Feel free to extend this pipeline with:
- Additional model architectures
- Different datasets
- Advanced training techniques
- Enhanced visualization options
- Performance optimizations
