"""
ResNet-50 Model Evaluation Module

This module provides comprehensive evaluation functionality including:
- Top-1 and Top-5 accuracy calculation
- Confusion matrix generation
- Per-class accuracy analysis
- Results export and visualization
"""

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix, classification_report
import json
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from tqdm import tqdm

from .utils import calculate_accuracy, AverageMeter

logger = logging.getLogger(__name__)


class ResNet50Evaluator:
    """Comprehensive evaluator for ResNet-50 model."""
    
    def __init__(self, model: nn.Module, device: torch.device, class_names: Optional[List[str]] = None):
        """
        Initialize the evaluator.
        
        Args:
            model (nn.Module): Trained ResNet-50 model
            device (torch.device): Device to run evaluation on
            class_names (List[str], optional): List of class names
        """
        self.model = model.to(device)
        self.device = device
        self.class_names = class_names
        
        # Evaluation results
        self.results = {}
        self.predictions = []
        self.targets = []
        self.probabilities = []
        
        logger.info(f"Evaluator initialized on device: {device}")
    
    def evaluate(self, test_loader: DataLoader, compute_confusion_matrix: bool = True,
                compute_per_class_accuracy: bool = True) -> Dict:
        """
        Comprehensive model evaluation.
        
        Args:
            test_loader (DataLoader): Test data loader
            compute_confusion_matrix (bool): Whether to compute confusion matrix
            compute_per_class_accuracy (bool): Whether to compute per-class accuracy
            
        Returns:
            Dict: Comprehensive evaluation results
        """
        logger.info("Starting model evaluation...")
        
        self.model.eval()
        
        # Metrics tracking
        losses = AverageMeter('Loss', ':.4f')
        top1 = AverageMeter('Acc@1', ':6.2f')
        top5 = AverageMeter('Acc@5', ':6.2f')
        
        # Loss function
        criterion = nn.CrossEntropyLoss()
        
        # Storage for detailed analysis
        all_predictions = []
        all_targets = []
        all_probabilities = []
        
        with torch.no_grad():
            for images, targets in tqdm(test_loader, desc='Evaluating'):
                images = images.to(self.device, non_blocking=True)
                targets = targets.to(self.device, non_blocking=True)
                
                # Forward pass
                outputs = self.model(images)
                loss = criterion(outputs, targets)
                
                # Calculate accuracies
                acc1, acc5 = calculate_accuracy(outputs, targets, topk=(1, 5))
                
                # Update metrics
                losses.update(loss.item(), images.size(0))
                top1.update(acc1.item(), images.size(0))
                top5.update(acc5.item(), images.size(0))
                
                # Store predictions and targets for detailed analysis
                probabilities = torch.softmax(outputs, dim=1)
                _, predicted = torch.max(outputs, 1)
                
                all_predictions.extend(predicted.cpu().numpy())
                all_targets.extend(targets.cpu().numpy())
                all_probabilities.extend(probabilities.cpu().numpy())
        
        # Store results
        self.predictions = np.array(all_predictions)
        self.targets = np.array(all_targets)
        self.probabilities = np.array(all_probabilities)
        
        # Basic metrics
        self.results = {
            'test_loss': losses.avg,
            'top1_accuracy': top1.avg,
            'top5_accuracy': top5.avg,
            'num_samples': len(all_targets),
            'num_classes': len(np.unique(all_targets))
        }
        
        logger.info(f"Evaluation completed:")
        logger.info(f"  Test Loss: {losses.avg:.4f}")
        logger.info(f"  Top-1 Accuracy: {top1.avg:.2f}%")
        logger.info(f"  Top-5 Accuracy: {top5.avg:.2f}%")
        logger.info(f"  Samples evaluated: {len(all_targets)}")
        
        # Compute additional metrics if requested
        if compute_confusion_matrix:
            self._compute_confusion_matrix()
        
        if compute_per_class_accuracy:
            self._compute_per_class_accuracy()
        
        return self.results
    
    def _compute_confusion_matrix(self):
        """Compute and store confusion matrix."""
        logger.info("Computing confusion matrix...")
        
        # Compute confusion matrix
        cm = confusion_matrix(self.targets, self.predictions)
        
        # Normalize confusion matrix
        cm_normalized = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]
        
        self.results['confusion_matrix'] = cm.tolist()
        self.results['confusion_matrix_normalized'] = cm_normalized.tolist()
        
        logger.info("Confusion matrix computed")
    
    def _compute_per_class_accuracy(self):
        """Compute per-class accuracy and other metrics."""
        logger.info("Computing per-class metrics...")
        
        # Get unique classes
        unique_classes = np.unique(self.targets)
        
        # Compute per-class accuracy
        per_class_accuracy = {}
        per_class_precision = {}
        per_class_recall = {}
        per_class_f1 = {}
        
        for class_idx in unique_classes:
            # Class-specific masks
            class_mask = (self.targets == class_idx)
            class_predictions = self.predictions[class_mask]
            class_targets = self.targets[class_mask]
            
            # Accuracy for this class
            accuracy = np.mean(class_predictions == class_targets) * 100
            
            # Precision, Recall, F1
            tp = np.sum((self.predictions == class_idx) & (self.targets == class_idx))
            fp = np.sum((self.predictions == class_idx) & (self.targets != class_idx))
            fn = np.sum((self.predictions != class_idx) & (self.targets == class_idx))
            
            precision = tp / (tp + fp) if (tp + fp) > 0 else 0
            recall = tp / (tp + fn) if (tp + fn) > 0 else 0
            f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
            
            class_name = self.class_names[class_idx] if self.class_names else f"Class_{class_idx}"
            
            per_class_accuracy[class_name] = accuracy
            per_class_precision[class_name] = precision * 100
            per_class_recall[class_name] = recall * 100
            per_class_f1[class_name] = f1 * 100
        
        self.results['per_class_accuracy'] = per_class_accuracy
        self.results['per_class_precision'] = per_class_precision
        self.results['per_class_recall'] = per_class_recall
        self.results['per_class_f1'] = per_class_f1
        
        # Summary statistics
        accuracies = list(per_class_accuracy.values())
        self.results['mean_per_class_accuracy'] = np.mean(accuracies)
        self.results['std_per_class_accuracy'] = np.std(accuracies)
        self.results['min_per_class_accuracy'] = np.min(accuracies)
        self.results['max_per_class_accuracy'] = np.max(accuracies)
        
        logger.info(f"Per-class accuracy - Mean: {np.mean(accuracies):.2f}%, Std: {np.std(accuracies):.2f}%")
    
    def plot_confusion_matrix(self, save_path: str, figsize: Tuple[int, int] = (12, 10)):
        """
        Plot and save confusion matrix.
        
        Args:
            save_path (str): Path to save the plot
            figsize (Tuple[int, int]): Figure size
        """
        if 'confusion_matrix_normalized' not in self.results:
            logger.warning("Confusion matrix not computed. Run evaluate() with compute_confusion_matrix=True first.")
            return
        
        logger.info(f"Plotting confusion matrix to {save_path}")
        
        # Create figure
        plt.figure(figsize=figsize)
        
        # Plot normalized confusion matrix
        cm_normalized = np.array(self.results['confusion_matrix_normalized'])
        
        # Use class names if available, otherwise use indices
        labels = self.class_names if self.class_names else [f"C{i}" for i in range(len(cm_normalized))]
        
        # Create heatmap
        sns.heatmap(
            cm_normalized,
            annot=True if len(cm_normalized) <= 20 else False,  # Only annotate if not too many classes
            fmt='.2f',
            cmap='Blues',
            xticklabels=labels,
            yticklabels=labels,
            cbar_kws={'label': 'Normalized Frequency'}
        )
        
        plt.title('Normalized Confusion Matrix')
        plt.xlabel('Predicted Label')
        plt.ylabel('True Label')
        plt.tight_layout()
        
        # Save plot
        Path(save_path).parent.mkdir(parents=True, exist_ok=True)
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"Confusion matrix plot saved to {save_path}")
    
    def plot_per_class_accuracy(self, save_path: str, figsize: Tuple[int, int] = (15, 8)):
        """
        Plot per-class accuracy.
        
        Args:
            save_path (str): Path to save the plot
            figsize (Tuple[int, int]): Figure size
        """
        if 'per_class_accuracy' not in self.results:
            logger.warning("Per-class accuracy not computed. Run evaluate() with compute_per_class_accuracy=True first.")
            return
        
        logger.info(f"Plotting per-class accuracy to {save_path}")
        
        # Prepare data
        class_names = list(self.results['per_class_accuracy'].keys())
        accuracies = list(self.results['per_class_accuracy'].values())
        
        # Create figure
        plt.figure(figsize=figsize)
        
        # Create bar plot
        bars = plt.bar(range(len(class_names)), accuracies, alpha=0.7)
        
        # Color bars based on accuracy
        for i, (bar, acc) in enumerate(zip(bars, accuracies)):
            if acc >= 80:
                bar.set_color('green')
            elif acc >= 60:
                bar.set_color('orange')
            else:
                bar.set_color('red')
        
        # Customize plot
        plt.xlabel('Class')
        plt.ylabel('Accuracy (%)')
        plt.title('Per-Class Accuracy')
        plt.xticks(range(len(class_names)), class_names, rotation=45, ha='right')
        plt.grid(axis='y', alpha=0.3)
        
        # Add mean line
        mean_acc = self.results['mean_per_class_accuracy']
        plt.axhline(y=mean_acc, color='red', linestyle='--', alpha=0.7, 
                   label=f'Mean: {mean_acc:.2f}%')
        plt.legend()
        
        plt.tight_layout()
        
        # Save plot
        Path(save_path).parent.mkdir(parents=True, exist_ok=True)
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"Per-class accuracy plot saved to {save_path}")
    
    def export_results(self, json_path: str, csv_path: Optional[str] = None):
        """
        Export evaluation results to JSON and optionally CSV.
        
        Args:
            json_path (str): Path to save JSON results
            csv_path (str, optional): Path to save CSV results
        """
        logger.info(f"Exporting results to {json_path}")
        
        # Ensure directory exists
        Path(json_path).parent.mkdir(parents=True, exist_ok=True)
        
        # Export to JSON
        with open(json_path, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        # Export to CSV if requested
        if csv_path and 'per_class_accuracy' in self.results:
            logger.info(f"Exporting per-class results to {csv_path}")
            
            # Create DataFrame with per-class metrics
            df_data = []
            for class_name in self.results['per_class_accuracy'].keys():
                row = {
                    'class_name': class_name,
                    'accuracy': self.results['per_class_accuracy'][class_name],
                    'precision': self.results.get('per_class_precision', {}).get(class_name, 0),
                    'recall': self.results.get('per_class_recall', {}).get(class_name, 0),
                    'f1_score': self.results.get('per_class_f1', {}).get(class_name, 0)
                }
                df_data.append(row)
            
            df = pd.DataFrame(df_data)
            df.to_csv(csv_path, index=False)
            
            logger.info(f"Per-class results exported to CSV: {csv_path}")
        
        logger.info("Results export completed")
    
    def print_summary(self):
        """Print a formatted summary of evaluation results."""
        if not self.results:
            print("No evaluation results available. Run evaluate() first.")
            return
        
        print("\n" + "="*60)
        print("ResNet-50 Model Evaluation Summary")
        print("="*60)
        
        print(f"Test Loss: {self.results['test_loss']:.4f}")
        print(f"Top-1 Accuracy: {self.results['top1_accuracy']:.2f}%")
        print(f"Top-5 Accuracy: {self.results['top5_accuracy']:.2f}%")
        print(f"Number of Samples: {self.results['num_samples']:,}")
        print(f"Number of Classes: {self.results['num_classes']}")
        
        if 'mean_per_class_accuracy' in self.results:
            print(f"\nPer-Class Accuracy Statistics:")
            print(f"  Mean: {self.results['mean_per_class_accuracy']:.2f}%")
            print(f"  Std:  {self.results['std_per_class_accuracy']:.2f}%")
            print(f"  Min:  {self.results['min_per_class_accuracy']:.2f}%")
            print(f"  Max:  {self.results['max_per_class_accuracy']:.2f}%")
        
        print("="*60)
