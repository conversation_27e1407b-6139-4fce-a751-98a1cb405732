"""
Main script for ResNet-50 Analysis and Training Pipeline

This script orchestrates the complete pipeline:
1. Model architecture analysis
2. Data loading and preprocessing
3. Model training with monitoring
4. Model evaluation and results export
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

import torch
import torchvision.models as models
import torch.nn as nn
from pathlib import Path
import argparse

from src.model_analysis import ResNet50Analyzer
from src.data_loader import ImageNet100DataLoader
from src.trainer import ResNet50Trainer
from src.evaluator import ResNet50Evaluator
from src.utils import setup_logging, get_device, print_system_info
from configs.config import Config


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='ResNet-50 Analysis and Training Pipeline')
    parser.add_argument('--phase', type=str, choices=['analysis', 'train', 'evaluate', 'all'], 
                       default='all', help='Which phase to run')
    parser.add_argument('--skip-analysis', action='store_true', 
                       help='Skip model analysis phase')
    parser.add_argument('--skip-training', action='store_true', 
                       help='Skip training phase')
    parser.add_argument('--load-checkpoint', type=str, default=None,
                       help='Path to checkpoint to load for evaluation')
    parser.add_argument('--epochs', type=int, default=None,
                       help='Override number of epochs')
    parser.add_argument('--batch-size', type=int, default=None,
                       help='Override batch size')
    
    return parser.parse_args()


def run_model_analysis():
    """Run Phase 1: Model Architecture Analysis."""
    logger.info("="*60)
    logger.info("PHASE 1: MODEL ARCHITECTURE ANALYSIS")
    logger.info("="*60)
    
    # Initialize analyzer
    analyzer = ResNet50Analyzer(
        pretrained=Config.PRETRAINED,
        num_classes=Config.NUM_CLASSES
    )
    
    # Perform complete analysis
    analysis_results = analyzer.perform_complete_analysis(
        input_size=Config.INPUT_SIZE
    )
    
    # Print summary
    analyzer.print_summary()
    
    # Export results
    analyzer.export_to_json(Config.MODEL_ANALYSIS_JSON)
    analyzer.export_to_csv(Config.MODEL_ANALYSIS_CSV)
    
    logger.info(f"Analysis results saved to:")
    logger.info(f"  JSON: {Config.MODEL_ANALYSIS_JSON}")
    logger.info(f"  CSV: {Config.MODEL_ANALYSIS_CSV}")
    
    return analysis_results


def setup_model_and_data():
    """Set up model and data loaders."""
    logger.info("Setting up model and data loaders...")
    
    # Initialize model
    model = models.resnet50(pretrained=Config.PRETRAINED)
    model.fc = nn.Linear(model.fc.in_features, Config.NUM_CLASSES)
    
    # Get device
    device = get_device()
    
    # Initialize data loader
    data_loader_manager = ImageNet100DataLoader(
        root_dir=Config.IMAGENET100_PATH,
        batch_size=Config.BATCH_SIZE,
        num_workers=Config.NUM_WORKERS,
        pin_memory=Config.PIN_MEMORY,
        validation_split=Config.VALIDATION_SPLIT
    )
    
    # Get data loaders
    train_loader, val_loader, test_loader = data_loader_manager.get_data_loaders()
    
    # Get dataset info
    dataset_info = data_loader_manager.get_dataset_info()
    logger.info(f"Dataset info: {dataset_info}")
    
    return model, device, train_loader, val_loader, test_loader, dataset_info


def run_training(model, device, train_loader, val_loader):
    """Run Phase 2: Model Training."""
    logger.info("="*60)
    logger.info("PHASE 2: MODEL TRAINING")
    logger.info("="*60)
    
    # Initialize trainer
    trainer = ResNet50Trainer(model, device, Config)
    
    # Start training
    training_results = trainer.train(train_loader, val_loader)
    
    logger.info("Training completed successfully!")
    logger.info(f"Best accuracy: {training_results['best_accuracy']:.2f}%")
    logger.info(f"Total epochs: {training_results['total_epochs']}")
    logger.info(f"Total time: {training_results['total_time']:.2f} seconds")
    
    return training_results


def run_evaluation(model, device, test_loader, dataset_info):
    """Run Phase 3: Model Evaluation."""
    logger.info("="*60)
    logger.info("PHASE 3: MODEL EVALUATION")
    logger.info("="*60)
    
    # Load best model if available
    best_model_path = Path(Config.CHECKPOINTS_DIR) / "best_model.pth"
    if best_model_path.exists():
        logger.info(f"Loading best model from {best_model_path}")
        checkpoint = torch.load(best_model_path, map_location=device)
        model.load_state_dict(checkpoint['model_state_dict'])
        logger.info(f"Loaded model with accuracy: {checkpoint.get('accuracy', 'Unknown'):.2f}%")
    else:
        logger.warning("Best model not found, using current model state")
    
    # Initialize evaluator
    evaluator = ResNet50Evaluator(model, device)
    
    # Run evaluation
    evaluation_results = evaluator.evaluate(
        test_loader,
        compute_confusion_matrix=Config.COMPUTE_CONFUSION_MATRIX,
        compute_per_class_accuracy=Config.COMPUTE_PER_CLASS_ACCURACY
    )
    
    # Print summary
    evaluator.print_summary()
    
    # Create visualizations
    results_dir = Path(Config.RESULTS_DIR)
    
    if Config.COMPUTE_CONFUSION_MATRIX:
        confusion_matrix_path = results_dir / "confusion_matrix.png"
        evaluator.plot_confusion_matrix(str(confusion_matrix_path))
    
    if Config.COMPUTE_PER_CLASS_ACCURACY:
        per_class_accuracy_path = results_dir / "per_class_accuracy.png"
        evaluator.plot_per_class_accuracy(str(per_class_accuracy_path))
    
    # Export results
    evaluation_json_path = results_dir / "evaluation_results.json"
    evaluation_csv_path = results_dir / "per_class_results.csv"
    evaluator.export_results(str(evaluation_json_path), str(evaluation_csv_path))
    
    logger.info(f"Evaluation results saved to:")
    logger.info(f"  JSON: {evaluation_json_path}")
    logger.info(f"  CSV: {evaluation_csv_path}")
    
    return evaluation_results


def main():
    """Main function to orchestrate the complete pipeline."""
    # Parse arguments
    args = parse_arguments()
    
    # Override config with command line arguments
    if args.epochs:
        Config.NUM_EPOCHS = args.epochs
    if args.batch_size:
        Config.BATCH_SIZE = args.batch_size
    
    # Print system information
    print_system_info()
    
    # Set up logging
    global logger
    logger = setup_logging(Config.TRAINING_LOG)
    
    # Create necessary directories
    Config.create_directories()
    
    # Set random seeds for reproducibility
    Config.set_random_seeds()
    
    # Print configuration
    Config.print_config()
    
    try:
        # Phase 1: Model Analysis
        if args.phase in ['analysis', 'all'] and not args.skip_analysis:
            analysis_results = run_model_analysis()
        
        # Setup model and data for training/evaluation
        if args.phase in ['train', 'evaluate', 'all']:
            model, device, train_loader, val_loader, test_loader, dataset_info = setup_model_and_data()
        
        # Phase 2: Training
        if args.phase in ['train', 'all'] and not args.skip_training:
            training_results = run_training(model, device, train_loader, val_loader)
        
        # Phase 3: Evaluation
        if args.phase in ['evaluate', 'all']:
            if test_loader is None:
                logger.warning("No test data available for evaluation")
            else:
                evaluation_results = run_evaluation(model, device, test_loader, dataset_info)
        
        logger.info("="*60)
        logger.info("PIPELINE COMPLETED SUCCESSFULLY!")
        logger.info("="*60)
        
        # Print final summary
        logger.info("Final Results Summary:")
        if args.phase in ['analysis', 'all'] and not args.skip_analysis:
            logger.info(f"✓ Model analysis completed - Results in {Config.RESULTS_DIR}")
        if args.phase in ['train', 'all'] and not args.skip_training:
            logger.info(f"✓ Training completed - Best model saved")
        if args.phase in ['evaluate', 'all'] and test_loader is not None:
            logger.info(f"✓ Evaluation completed - Results in {Config.RESULTS_DIR}")
        
    except Exception as e:
        logger.error(f"Pipeline failed with error: {str(e)}")
        raise


if __name__ == "__main__":
    main()
