"""
Configuration file for ResNet-50 Analysis and Training Pipeline
"""

import os
import torch

class Config:
    """Configuration class containing all parameters for the pipeline."""
    
    # ==================== PATHS ====================
    # Dataset path
    IMAGENET100_PATH = r"C:\Users\<USER>\Desktop\new\imagenet100"
    
    # Project paths
    PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    RESULTS_DIR = os.path.join(PROJECT_ROOT, "results")
    CHECKPOINTS_DIR = os.path.join(PROJECT_ROOT, "checkpoints")
    LOGS_DIR = os.path.join(PROJECT_ROOT, "logs")
    
    # Output files
    MODEL_ANALYSIS_JSON = os.path.join(RESULTS_DIR, "resnet50_analysis.json")
    MODEL_ANALYSIS_CSV = os.path.join(RESULTS_DIR, "resnet50_analysis.csv")
    TRAINING_LOG = os.path.join(LOGS_DIR, "training.log")
    EVALUATION_RESULTS = os.path.join(RESULTS_DIR, "evaluation_results.json")
    FINAL_MODEL_PATH = os.path.join(CHECKPOINTS_DIR, "resnet50_final.pth")
    
    # ==================== MODEL PARAMETERS ====================
    MODEL_NAME = "resnet50"
    NUM_CLASSES = 100  # ImageNet100 has 100 classes
    PRETRAINED = True  # Start with ImageNet pretrained weights
    
    # ==================== TRAINING PARAMETERS ====================
    # Data loading
    BATCH_SIZE = 32
    NUM_WORKERS = 4
    PIN_MEMORY = True
    
    # Training hyperparameters
    LEARNING_RATE = 0.001
    WEIGHT_DECAY = 1e-4
    MOMENTUM = 0.9
    NUM_EPOCHS = 50
    
    # Optimizer and scheduler
    OPTIMIZER = "SGD"  # Options: "SGD", "Adam", "AdamW"
    SCHEDULER = "StepLR"  # Options: "StepLR", "CosineAnnealingLR", "ReduceLROnPlateau"
    STEP_SIZE = 15  # For StepLR
    GAMMA = 0.1  # Learning rate decay factor
    
    # Loss function
    LOSS_FUNCTION = "CrossEntropyLoss"
    
    # ==================== DATA PREPROCESSING ====================
    # Image preprocessing
    IMAGE_SIZE = 224
    CROP_SIZE = 224
    MEAN = [0.485, 0.456, 0.406]  # ImageNet mean
    STD = [0.229, 0.224, 0.225]   # ImageNet std
    
    # Data augmentation
    HORIZONTAL_FLIP_PROB = 0.5
    ROTATION_DEGREES = 10
    COLOR_JITTER_BRIGHTNESS = 0.2
    COLOR_JITTER_CONTRAST = 0.2
    COLOR_JITTER_SATURATION = 0.2
    COLOR_JITTER_HUE = 0.1
    
    # ==================== TRAINING MONITORING ====================
    # Logging and checkpointing
    LOG_INTERVAL = 10  # Log every N batches
    SAVE_INTERVAL = 5  # Save checkpoint every N epochs
    EARLY_STOPPING_PATIENCE = 10  # Stop if no improvement for N epochs
    
    # Validation
    VALIDATION_SPLIT = 0.2  # 20% of training data for validation
    
    # ==================== EVALUATION PARAMETERS ====================
    # Metrics to compute
    COMPUTE_TOP1_ACCURACY = True
    COMPUTE_TOP5_ACCURACY = True
    COMPUTE_CONFUSION_MATRIX = True
    COMPUTE_PER_CLASS_ACCURACY = True
    
    # ==================== REPRODUCIBILITY ====================
    # Random seeds for reproducibility
    RANDOM_SEED = 42
    TORCH_SEED = 42
    NUMPY_SEED = 42
    
    # CUDA settings
    DEVICE = "cuda" if torch.cuda.is_available() else "cpu"
    CUDA_DETERMINISTIC = True
    CUDA_BENCHMARK = False  # Set to True for better performance if input sizes are fixed
    
    # ==================== ANALYSIS PARAMETERS ====================
    # Model analysis settings
    INPUT_SIZE = (1, 3, 224, 224)  # (batch_size, channels, height, width)
    ANALYZE_MEMORY = True
    ANALYZE_FLOPS = True
    EXPORT_DETAILED_ANALYSIS = True
    
    @classmethod
    def create_directories(cls):
        """Create necessary directories if they don't exist."""
        directories = [
            cls.RESULTS_DIR,
            cls.CHECKPOINTS_DIR,
            cls.LOGS_DIR
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    @classmethod
    def set_random_seeds(cls):
        """Set random seeds for reproducibility."""
        import random
        import numpy as np
        
        random.seed(cls.RANDOM_SEED)
        np.random.seed(cls.NUMPY_SEED)
        torch.manual_seed(cls.TORCH_SEED)
        
        if torch.cuda.is_available():
            torch.cuda.manual_seed(cls.TORCH_SEED)
            torch.cuda.manual_seed_all(cls.TORCH_SEED)
            torch.backends.cudnn.deterministic = cls.CUDA_DETERMINISTIC
            torch.backends.cudnn.benchmark = cls.CUDA_BENCHMARK
    
    @classmethod
    def print_config(cls):
        """Print current configuration."""
        print("=" * 50)
        print("ResNet-50 Pipeline Configuration")
        print("=" * 50)
        print(f"Device: {cls.DEVICE}")
        print(f"Dataset Path: {cls.IMAGENET100_PATH}")
        print(f"Batch Size: {cls.BATCH_SIZE}")
        print(f"Learning Rate: {cls.LEARNING_RATE}")
        print(f"Number of Epochs: {cls.NUM_EPOCHS}")
        print(f"Optimizer: {cls.OPTIMIZER}")
        print(f"Random Seed: {cls.RANDOM_SEED}")
        print("=" * 50)
