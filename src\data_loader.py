"""
ImageNet100 Dataset Loader and Preprocessing Module

This module provides data loading functionality for ImageNet100 dataset with:
- Proper data preprocessing and augmentation
- Train/validation/test splits
- Efficient data loading with PyTorch DataLoader
"""

import os
import torch
import torchvision
from torch.utils.data import DataLoader, Dataset, random_split
from torchvision import transforms
from PIL import Image
import json
import logging
from pathlib import Path
from typing import Tuple, Dict, List, Optional

logger = logging.getLogger(__name__)


class ImageNet100Dataset(Dataset):
    """Custom dataset class for ImageNet100."""
    
    def __init__(self, root_dir: str, split: str = 'train', transform: Optional[transforms.Compose] = None):
        """
        Initialize ImageNet100 dataset.
        
        Args:
            root_dir (str): Root directory of ImageNet100 dataset
            split (str): Dataset split ('train', 'val', or 'test')
            transform (transforms.Compose, optional): Image transformations
        """
        self.root_dir = Path(root_dir)
        self.split = split
        self.transform = transform
        
        # Set up paths
        self.split_dir = self.root_dir / split
        
        if not self.split_dir.exists():
            raise FileNotFoundError(f"Split directory not found: {self.split_dir}")
        
        # Load class names and create class-to-index mapping
        self.classes = sorted([d.name for d in self.split_dir.iterdir() if d.is_dir()])
        self.class_to_idx = {cls_name: idx for idx, cls_name in enumerate(self.classes)}
        self.num_classes = len(self.classes)
        
        # Load all image paths and labels
        self.samples = []
        self._load_samples()
        
        logger.info(f"Loaded {len(self.samples)} samples from {split} split")
        logger.info(f"Number of classes: {self.num_classes}")
    
    def _load_samples(self):
        """Load all image paths and their corresponding labels."""
        for class_name in self.classes:
            class_dir = self.split_dir / class_name
            class_idx = self.class_to_idx[class_name]
            
            # Get all image files in the class directory
            image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'}
            for img_path in class_dir.iterdir():
                if img_path.suffix.lower() in image_extensions:
                    self.samples.append((str(img_path), class_idx))
    
    def __len__(self) -> int:
        """Return the number of samples in the dataset."""
        return len(self.samples)
    
    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, int]:
        """
        Get a sample from the dataset.
        
        Args:
            idx (int): Sample index
            
        Returns:
            Tuple[torch.Tensor, int]: (image, label)
        """
        img_path, label = self.samples[idx]
        
        # Load image
        try:
            image = Image.open(img_path).convert('RGB')
        except Exception as e:
            logger.warning(f"Error loading image {img_path}: {e}")
            # Return a black image as fallback
            image = Image.new('RGB', (224, 224), (0, 0, 0))
        
        # Apply transformations
        if self.transform:
            image = self.transform(image)
        
        return image, label
    
    def get_class_distribution(self) -> Dict[str, int]:
        """Get the distribution of samples per class."""
        distribution = {}
        for _, label in self.samples:
            class_name = self.classes[label]
            distribution[class_name] = distribution.get(class_name, 0) + 1
        return distribution


class ImageNet100DataLoader:
    """Data loader manager for ImageNet100 dataset."""
    
    def __init__(self, root_dir: str, batch_size: int = 32, num_workers: int = 4, 
                 pin_memory: bool = True, validation_split: float = 0.2):
        """
        Initialize the data loader manager.
        
        Args:
            root_dir (str): Root directory of ImageNet100 dataset
            batch_size (int): Batch size for data loading
            num_workers (int): Number of worker processes for data loading
            pin_memory (bool): Whether to pin memory for faster GPU transfer
            validation_split (float): Fraction of training data to use for validation
        """
        self.root_dir = root_dir
        self.batch_size = batch_size
        self.num_workers = num_workers
        self.pin_memory = pin_memory
        self.validation_split = validation_split
        
        # Initialize transforms
        self.train_transform = None
        self.val_transform = None
        self.test_transform = None
        
        # Initialize datasets and loaders
        self.train_dataset = None
        self.val_dataset = None
        self.test_dataset = None
        
        self.train_loader = None
        self.val_loader = None
        self.test_loader = None
        
        self._setup_transforms()
    
    def _setup_transforms(self):
        """Set up data transformations for training, validation, and testing."""
        # ImageNet normalization values
        normalize = transforms.Normalize(
            mean=[0.485, 0.456, 0.406],
            std=[0.229, 0.224, 0.225]
        )
        
        # Training transforms with augmentation
        self.train_transform = transforms.Compose([
            transforms.Resize(256),
            transforms.RandomResizedCrop(224),
            transforms.RandomHorizontalFlip(p=0.5),
            transforms.ColorJitter(
                brightness=0.2,
                contrast=0.2,
                saturation=0.2,
                hue=0.1
            ),
            transforms.RandomRotation(degrees=10),
            transforms.ToTensor(),
            normalize
        ])
        
        # Validation and test transforms (no augmentation)
        self.val_transform = transforms.Compose([
            transforms.Resize(256),
            transforms.CenterCrop(224),
            transforms.ToTensor(),
            normalize
        ])
        
        self.test_transform = self.val_transform
        
        logger.info("Data transforms initialized")
    
    def setup_datasets(self):
        """Set up train, validation, and test datasets."""
        logger.info("Setting up datasets...")
        
        # Check if dataset directory exists
        if not os.path.exists(self.root_dir):
            raise FileNotFoundError(f"Dataset directory not found: {self.root_dir}")
        
        # Load training dataset
        try:
            full_train_dataset = ImageNet100Dataset(
                root_dir=self.root_dir,
                split='train',
                transform=self.train_transform
            )
            
            # Split training data into train and validation
            if self.validation_split > 0:
                val_size = int(len(full_train_dataset) * self.validation_split)
                train_size = len(full_train_dataset) - val_size
                
                self.train_dataset, val_dataset_temp = random_split(
                    full_train_dataset, 
                    [train_size, val_size],
                    generator=torch.Generator().manual_seed(42)
                )
                
                # Create validation dataset with different transforms
                self.val_dataset = ImageNet100Dataset(
                    root_dir=self.root_dir,
                    split='train',
                    transform=self.val_transform
                )
                # Use the same indices as val_dataset_temp
                self.val_dataset.samples = [full_train_dataset.samples[i] for i in val_dataset_temp.indices]
                
                logger.info(f"Training samples: {len(self.train_dataset)}")
                logger.info(f"Validation samples: {len(self.val_dataset)}")
            else:
                self.train_dataset = full_train_dataset
                logger.info(f"Training samples: {len(self.train_dataset)}")
        
        except Exception as e:
            logger.error(f"Error loading training dataset: {e}")
            raise
        
        # Load test dataset if available
        try:
            test_dir = os.path.join(self.root_dir, 'val')  # ImageNet100 uses 'val' as test set
            if os.path.exists(test_dir):
                self.test_dataset = ImageNet100Dataset(
                    root_dir=self.root_dir,
                    split='val',
                    transform=self.test_transform
                )
                logger.info(f"Test samples: {len(self.test_dataset)}")
            else:
                logger.warning("Test dataset not found")
        
        except Exception as e:
            logger.warning(f"Error loading test dataset: {e}")
        
        logger.info("Datasets setup completed")
    
    def create_data_loaders(self):
        """Create PyTorch DataLoaders for all datasets."""
        logger.info("Creating data loaders...")
        
        if self.train_dataset is None:
            raise ValueError("Datasets not initialized. Call setup_datasets() first.")
        
        # Training data loader
        self.train_loader = DataLoader(
            self.train_dataset,
            batch_size=self.batch_size,
            shuffle=True,
            num_workers=self.num_workers,
            pin_memory=self.pin_memory,
            drop_last=True
        )
        
        # Validation data loader
        if self.val_dataset is not None:
            self.val_loader = DataLoader(
                self.val_dataset,
                batch_size=self.batch_size,
                shuffle=False,
                num_workers=self.num_workers,
                pin_memory=self.pin_memory
            )
        
        # Test data loader
        if self.test_dataset is not None:
            self.test_loader = DataLoader(
                self.test_dataset,
                batch_size=self.batch_size,
                shuffle=False,
                num_workers=self.num_workers,
                pin_memory=self.pin_memory
            )
        
        logger.info("Data loaders created successfully")
    
    def get_data_loaders(self) -> Tuple[DataLoader, Optional[DataLoader], Optional[DataLoader]]:
        """
        Get all data loaders.
        
        Returns:
            Tuple[DataLoader, Optional[DataLoader], Optional[DataLoader]]: 
                (train_loader, val_loader, test_loader)
        """
        if self.train_loader is None:
            self.setup_datasets()
            self.create_data_loaders()
        
        return self.train_loader, self.val_loader, self.test_loader
    
    def get_dataset_info(self) -> Dict:
        """Get information about the datasets."""
        if self.train_dataset is None:
            self.setup_datasets()
        
        info = {
            'num_classes': self.train_dataset.dataset.num_classes if hasattr(self.train_dataset, 'dataset') else len(self.train_dataset.classes),
            'train_samples': len(self.train_dataset) if self.train_dataset else 0,
            'val_samples': len(self.val_dataset) if self.val_dataset else 0,
            'test_samples': len(self.test_dataset) if self.test_dataset else 0,
            'batch_size': self.batch_size,
            'num_workers': self.num_workers
        }
        
        return info
