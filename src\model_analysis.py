"""
ResNet-50 Model Architecture Analysis Module

This module provides comprehensive analysis of ResNet-50 architecture including:
- Layer-by-layer analysis (name, type, dimensions, parameters)
- FLOP computation
- Memory usage analysis
- Export to structured formats (JSON/CSV)
"""

import torch
import torch.nn as nn
import torchvision.models as models
from torchinfo import summary
import json
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any
import logging
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ResNet50Analyzer:
    """Comprehensive analyzer for ResNet-50 architecture."""
    
    def __init__(self, pretrained: bool = True, num_classes: int = 1000):
        """
        Initialize the ResNet-50 analyzer.
        
        Args:
            pretrained (bool): Whether to load pretrained weights
            num_classes (int): Number of output classes
        """
        self.pretrained = pretrained
        self.num_classes = num_classes
        self.model = None
        self.analysis_results = {}
        
    def load_model(self) -> nn.Module:
        """
        Load ResNet-50 model from torchvision.
        
        Returns:
            nn.Module: Loaded ResNet-50 model
        """
        logger.info(f"Loading ResNet-50 model (pretrained={self.pretrained})")
        
        # Load the model
        self.model = models.resnet50(pretrained=self.pretrained)
        
        # Modify the final layer if needed
        if self.num_classes != 1000:
            self.model.fc = nn.Linear(self.model.fc.in_features, self.num_classes)
            logger.info(f"Modified final layer for {self.num_classes} classes")
        
        # Set to evaluation mode for analysis
        self.model.eval()
        
        logger.info("ResNet-50 model loaded successfully")
        return self.model
    
    def get_model_summary(self, input_size: Tuple[int, ...] = (1, 3, 224, 224)) -> Dict[str, Any]:
        """
        Get comprehensive model summary using torchinfo.
        
        Args:
            input_size (Tuple): Input tensor size (batch, channels, height, width)
            
        Returns:
            Dict: Model summary information
        """
        if self.model is None:
            self.load_model()
        
        logger.info(f"Generating model summary for input size: {input_size}")
        
        # Generate summary
        model_stats = summary(
            self.model,
            input_size=input_size,
            col_names=["input_size", "output_size", "num_params", "params_percent", "kernel_size", "mult_adds"],
            verbose=0
        )
        
        # Extract key information
        summary_info = {
            "total_params": model_stats.total_params,
            "trainable_params": model_stats.trainable_params,
            "non_trainable_params": model_stats.total_params - model_stats.trainable_params,
            "total_mult_adds": model_stats.total_mult_adds,
            "input_size": input_size,
            "forward_backward_pass_size_mb": model_stats.total_input + model_stats.total_output_bytes,
            "params_size_mb": model_stats.total_param_bytes / (1024**2),
            "estimated_total_size_mb": model_stats.total_param_bytes / (1024**2) + 
                                     (model_stats.total_input + model_stats.total_output_bytes) / (1024**2)
        }
        
        return summary_info
    
    def analyze_layers(self, input_size: Tuple[int, ...] = (1, 3, 224, 224)) -> List[Dict[str, Any]]:
        """
        Perform detailed layer-by-layer analysis.
        
        Args:
            input_size (Tuple): Input tensor size
            
        Returns:
            List[Dict]: Detailed information for each layer
        """
        if self.model is None:
            self.load_model()
        
        logger.info("Performing layer-by-layer analysis")
        
        layers_info = []
        
        # Create a dummy input
        dummy_input = torch.randn(input_size)
        
        # Hook function to capture layer information
        def hook_fn(module, input, output):
            layer_info = {
                "layer_name": str(module.__class__.__name__),
                "layer_type": type(module).__name__,
                "input_shape": list(input[0].shape) if isinstance(input, tuple) and len(input) > 0 else "N/A",
                "output_shape": list(output.shape) if hasattr(output, 'shape') else "N/A",
                "num_parameters": sum(p.numel() for p in module.parameters()),
                "trainable_parameters": sum(p.numel() for p in module.parameters() if p.requires_grad),
                "has_bias": any(name.endswith('.bias') for name, _ in module.named_parameters()),
                "module_str": str(module)
            }
            layers_info.append(layer_info)
        
        # Register hooks for all modules
        hooks = []
        for _, module in self.model.named_modules():
            if len(list(module.children())) == 0:  # Only leaf modules
                hook = module.register_forward_hook(hook_fn)
                hooks.append(hook)
        
        # Forward pass to trigger hooks
        with torch.no_grad():
            _ = self.model(dummy_input)
        
        # Remove hooks
        for hook in hooks:
            hook.remove()
        
        logger.info(f"Analyzed {len(layers_info)} layers")
        return layers_info

    def calculate_flops(self, input_size: Tuple[int, ...] = (1, 3, 224, 224)) -> Dict[str, Any]:
        """
        Calculate FLOPs (Floating Point Operations) for the model.

        Args:
            input_size (Tuple): Input tensor size

        Returns:
            Dict: FLOP analysis results
        """
        if self.model is None:
            self.load_model()

        logger.info("Calculating FLOPs")

        try:
            from thop import profile, clever_format

            # Create dummy input
            dummy_input = torch.randn(input_size)

            # Calculate FLOPs and parameters
            flops, params = profile(self.model, inputs=(dummy_input,), verbose=False)

            # Format the results
            flops_formatted, params_formatted = clever_format([flops, params], "%.3f")

            flop_info = {
                "total_flops": flops,
                "total_flops_formatted": flops_formatted,
                "total_params_from_flop_analysis": params,
                "total_params_formatted": params_formatted,
                "flops_per_parameter": flops / params if params > 0 else 0,
                "input_size": input_size
            }

            logger.info(f"FLOPs: {flops_formatted}, Params: {params_formatted}")
            return flop_info

        except ImportError:
            logger.warning("thop library not available. FLOP calculation skipped.")
            return {"error": "thop library not available"}

    def calculate_memory_usage(self, input_size: Tuple[int, ...] = (1, 3, 224, 224)) -> Dict[str, Any]:
        """
        Calculate memory usage for the model.

        Args:
            input_size (Tuple): Input tensor size

        Returns:
            Dict: Memory usage analysis
        """
        if self.model is None:
            self.load_model()

        logger.info("Calculating memory usage")

        # Calculate parameter memory
        param_memory = sum(p.numel() * p.element_size() for p in self.model.parameters())

        # Calculate buffer memory
        buffer_memory = sum(b.numel() * b.element_size() for b in self.model.buffers())

        # Estimate activation memory (rough approximation)
        dummy_input = torch.randn(input_size)
        activation_memory = 0

        def hook_fn(module, input, output):
            nonlocal activation_memory
            if hasattr(output, 'numel') and hasattr(output, 'element_size'):
                activation_memory += output.numel() * output.element_size()

        hooks = []
        for module in self.model.modules():
            if len(list(module.children())) == 0:
                hook = module.register_forward_hook(hook_fn)
                hooks.append(hook)

        with torch.no_grad():
            _ = self.model(dummy_input)

        for hook in hooks:
            hook.remove()

        # Convert to MB
        param_memory_mb = param_memory / (1024**2)
        buffer_memory_mb = buffer_memory / (1024**2)
        activation_memory_mb = activation_memory / (1024**2)
        total_memory_mb = param_memory_mb + buffer_memory_mb + activation_memory_mb

        memory_info = {
            "parameter_memory_bytes": param_memory,
            "parameter_memory_mb": param_memory_mb,
            "buffer_memory_bytes": buffer_memory,
            "buffer_memory_mb": buffer_memory_mb,
            "activation_memory_bytes": activation_memory,
            "activation_memory_mb": activation_memory_mb,
            "total_memory_bytes": param_memory + buffer_memory + activation_memory,
            "total_memory_mb": total_memory_mb,
            "input_size": input_size
        }

        logger.info(f"Total memory usage: {total_memory_mb:.2f} MB")
        return memory_info

    def perform_complete_analysis(self, input_size: Tuple[int, ...] = (1, 3, 224, 224)) -> Dict[str, Any]:
        """
        Perform complete model analysis.

        Args:
            input_size (Tuple): Input tensor size

        Returns:
            Dict: Complete analysis results
        """
        logger.info("Starting complete ResNet-50 analysis")

        # Load model if not already loaded
        if self.model is None:
            self.load_model()

        # Perform all analyses
        model_summary = self.get_model_summary(input_size)
        layers_analysis = self.analyze_layers(input_size)
        flop_analysis = self.calculate_flops(input_size)
        memory_analysis = self.calculate_memory_usage(input_size)

        # Compile complete results
        complete_analysis = {
            "model_info": {
                "model_name": "ResNet-50",
                "pretrained": self.pretrained,
                "num_classes": self.num_classes,
                "input_size": input_size
            },
            "summary": model_summary,
            "layers": layers_analysis,
            "flops": flop_analysis,
            "memory": memory_analysis,
            "analysis_metadata": {
                "pytorch_version": torch.__version__,
                "total_layers_analyzed": len(layers_analysis)
            }
        }

        self.analysis_results = complete_analysis
        logger.info("Complete analysis finished")

        return complete_analysis

    def export_to_json(self, filepath: str) -> None:
        """
        Export analysis results to JSON format.

        Args:
            filepath (str): Path to save JSON file
        """
        if not self.analysis_results:
            raise ValueError("No analysis results available. Run perform_complete_analysis() first.")

        logger.info(f"Exporting analysis to JSON: {filepath}")

        # Ensure directory exists
        Path(filepath).parent.mkdir(parents=True, exist_ok=True)

        # Convert numpy types to native Python types for JSON serialization
        def convert_numpy_types(obj):
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, dict):
                return {key: convert_numpy_types(value) for key, value in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy_types(item) for item in obj]
            else:
                return obj

        # Convert and save
        json_data = convert_numpy_types(self.analysis_results)

        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, indent=2, ensure_ascii=False)

        logger.info(f"Analysis exported to JSON successfully: {filepath}")

    def export_to_csv(self, filepath: str) -> None:
        """
        Export layer analysis to CSV format.

        Args:
            filepath (str): Path to save CSV file
        """
        if not self.analysis_results or 'layers' not in self.analysis_results:
            raise ValueError("No layer analysis results available. Run perform_complete_analysis() first.")

        logger.info(f"Exporting layer analysis to CSV: {filepath}")

        # Ensure directory exists
        Path(filepath).parent.mkdir(parents=True, exist_ok=True)

        # Convert layers data to DataFrame
        layers_data = self.analysis_results['layers']
        df = pd.DataFrame(layers_data)

        # Add summary information as additional rows
        summary_data = self.analysis_results['summary']
        flop_data = self.analysis_results.get('flops', {})
        memory_data = self.analysis_results.get('memory', {})

        # Create summary DataFrame
        summary_rows = [
            {'layer_name': 'SUMMARY', 'layer_type': 'Total Parameters', 'num_parameters': summary_data.get('total_params', 0)},
            {'layer_name': 'SUMMARY', 'layer_type': 'Trainable Parameters', 'num_parameters': summary_data.get('trainable_params', 0)},
            {'layer_name': 'SUMMARY', 'layer_type': 'Total FLOPs', 'num_parameters': flop_data.get('total_flops', 0)},
            {'layer_name': 'SUMMARY', 'layer_type': 'Total Memory (MB)', 'num_parameters': memory_data.get('total_memory_mb', 0)},
        ]

        summary_df = pd.DataFrame(summary_rows)

        # Combine DataFrames
        final_df = pd.concat([df, summary_df], ignore_index=True)

        # Save to CSV
        final_df.to_csv(filepath, index=False, encoding='utf-8')

        logger.info(f"Layer analysis exported to CSV successfully: {filepath}")

    def print_summary(self) -> None:
        """Print a formatted summary of the analysis results."""
        if not self.analysis_results:
            print("No analysis results available. Run perform_complete_analysis() first.")
            return

        print("\n" + "="*60)
        print("ResNet-50 Model Analysis Summary")
        print("="*60)

        # Model info
        model_info = self.analysis_results['model_info']
        print(f"Model: {model_info['model_name']}")
        print(f"Pretrained: {model_info['pretrained']}")
        print(f"Number of Classes: {model_info['num_classes']}")
        print(f"Input Size: {model_info['input_size']}")

        # Summary statistics
        summary = self.analysis_results['summary']
        print(f"\nTotal Parameters: {summary['total_params']:,}")
        print(f"Trainable Parameters: {summary['trainable_params']:,}")
        print(f"Model Size: {summary['params_size_mb']:.2f} MB")

        # FLOPs
        if 'flops' in self.analysis_results and 'total_flops' in self.analysis_results['flops']:
            flops = self.analysis_results['flops']
            print(f"Total FLOPs: {flops.get('total_flops_formatted', 'N/A')}")

        # Memory
        if 'memory' in self.analysis_results:
            memory = self.analysis_results['memory']
            print(f"Total Memory Usage: {memory['total_memory_mb']:.2f} MB")

        # Layer count
        layers_count = len(self.analysis_results.get('layers', []))
        print(f"Total Layers Analyzed: {layers_count}")

        print("="*60)
