{"model_info": {"model_name": "ResNet-50", "pretrained": true, "num_classes": 100, "input_size": [1, 3, 224, 224]}, "summary": {"total_params": 23712932, "trainable_params": 23712932, "non_trainable_params": 0, "total_mult_adds": 4087394276, "input_size": [1, 3, 224, 224], "forward_backward_pass_size_mb": 178426728, "params_size_mb": 90.45765686035156, "estimated_total_size_mb": 260.61864471435547}, "layers": [{"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 3, 224, 224], "output_shape": [1, 64, 112, 112], "num_parameters": 9408, "trainable_parameters": 9408, "has_bias": false, "module_str": "Conv2d(3, 64, kernel_size=(7, 7), stride=(2, 2), padding=(3, 3), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 64, 112, 112], "output_shape": [1, 64, 112, 112], "num_parameters": 128, "trainable_parameters": 128, "has_bias": false, "module_str": "BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 64, 112, 112], "output_shape": [1, 64, 112, 112], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "MaxPool2d", "layer_type": "MaxPool2d", "input_shape": [1, 64, 112, 112], "output_shape": [1, 64, 56, 56], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "MaxPool2d(kernel_size=3, stride=2, padding=1, dilation=1, ceil_mode=False)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 64, 56, 56], "output_shape": [1, 64, 56, 56], "num_parameters": 4096, "trainable_parameters": 4096, "has_bias": false, "module_str": "Conv2d(64, 64, kernel_size=(1, 1), stride=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 64, 56, 56], "output_shape": [1, 64, 56, 56], "num_parameters": 128, "trainable_parameters": 128, "has_bias": false, "module_str": "BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 64, 56, 56], "output_shape": [1, 64, 56, 56], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 64, 56, 56], "output_shape": [1, 64, 56, 56], "num_parameters": 36864, "trainable_parameters": 36864, "has_bias": false, "module_str": "Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 64, 56, 56], "output_shape": [1, 64, 56, 56], "num_parameters": 128, "trainable_parameters": 128, "has_bias": false, "module_str": "BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 64, 56, 56], "output_shape": [1, 64, 56, 56], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 64, 56, 56], "output_shape": [1, 256, 56, 56], "num_parameters": 16384, "trainable_parameters": 16384, "has_bias": false, "module_str": "Conv2d(64, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 256, 56, 56], "output_shape": [1, 256, 56, 56], "num_parameters": 512, "trainable_parameters": 512, "has_bias": false, "module_str": "BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 64, 56, 56], "output_shape": [1, 256, 56, 56], "num_parameters": 16384, "trainable_parameters": 16384, "has_bias": false, "module_str": "Conv2d(64, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 256, 56, 56], "output_shape": [1, 256, 56, 56], "num_parameters": 512, "trainable_parameters": 512, "has_bias": false, "module_str": "BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 256, 56, 56], "output_shape": [1, 256, 56, 56], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 256, 56, 56], "output_shape": [1, 64, 56, 56], "num_parameters": 16384, "trainable_parameters": 16384, "has_bias": false, "module_str": "Conv2d(256, 64, kernel_size=(1, 1), stride=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 64, 56, 56], "output_shape": [1, 64, 56, 56], "num_parameters": 128, "trainable_parameters": 128, "has_bias": false, "module_str": "BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 64, 56, 56], "output_shape": [1, 64, 56, 56], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 64, 56, 56], "output_shape": [1, 64, 56, 56], "num_parameters": 36864, "trainable_parameters": 36864, "has_bias": false, "module_str": "Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 64, 56, 56], "output_shape": [1, 64, 56, 56], "num_parameters": 128, "trainable_parameters": 128, "has_bias": false, "module_str": "BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 64, 56, 56], "output_shape": [1, 64, 56, 56], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 64, 56, 56], "output_shape": [1, 256, 56, 56], "num_parameters": 16384, "trainable_parameters": 16384, "has_bias": false, "module_str": "Conv2d(64, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 256, 56, 56], "output_shape": [1, 256, 56, 56], "num_parameters": 512, "trainable_parameters": 512, "has_bias": false, "module_str": "BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 256, 56, 56], "output_shape": [1, 256, 56, 56], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 256, 56, 56], "output_shape": [1, 64, 56, 56], "num_parameters": 16384, "trainable_parameters": 16384, "has_bias": false, "module_str": "Conv2d(256, 64, kernel_size=(1, 1), stride=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 64, 56, 56], "output_shape": [1, 64, 56, 56], "num_parameters": 128, "trainable_parameters": 128, "has_bias": false, "module_str": "BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 64, 56, 56], "output_shape": [1, 64, 56, 56], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 64, 56, 56], "output_shape": [1, 64, 56, 56], "num_parameters": 36864, "trainable_parameters": 36864, "has_bias": false, "module_str": "Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 64, 56, 56], "output_shape": [1, 64, 56, 56], "num_parameters": 128, "trainable_parameters": 128, "has_bias": false, "module_str": "BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 64, 56, 56], "output_shape": [1, 64, 56, 56], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 64, 56, 56], "output_shape": [1, 256, 56, 56], "num_parameters": 16384, "trainable_parameters": 16384, "has_bias": false, "module_str": "Conv2d(64, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 256, 56, 56], "output_shape": [1, 256, 56, 56], "num_parameters": 512, "trainable_parameters": 512, "has_bias": false, "module_str": "BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 256, 56, 56], "output_shape": [1, 256, 56, 56], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 256, 56, 56], "output_shape": [1, 128, 56, 56], "num_parameters": 32768, "trainable_parameters": 32768, "has_bias": false, "module_str": "Conv2d(256, 128, kernel_size=(1, 1), stride=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 128, 56, 56], "output_shape": [1, 128, 56, 56], "num_parameters": 256, "trainable_parameters": 256, "has_bias": false, "module_str": "BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 128, 56, 56], "output_shape": [1, 128, 56, 56], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 128, 56, 56], "output_shape": [1, 128, 28, 28], "num_parameters": 147456, "trainable_parameters": 147456, "has_bias": false, "module_str": "Conv2d(128, 128, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 128, 28, 28], "output_shape": [1, 128, 28, 28], "num_parameters": 256, "trainable_parameters": 256, "has_bias": false, "module_str": "BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 128, 28, 28], "output_shape": [1, 128, 28, 28], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 128, 28, 28], "output_shape": [1, 512, 28, 28], "num_parameters": 65536, "trainable_parameters": 65536, "has_bias": false, "module_str": "Conv2d(128, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 512, 28, 28], "output_shape": [1, 512, 28, 28], "num_parameters": 1024, "trainable_parameters": 1024, "has_bias": false, "module_str": "BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 256, 56, 56], "output_shape": [1, 512, 28, 28], "num_parameters": 131072, "trainable_parameters": 131072, "has_bias": false, "module_str": "Conv2d(256, 512, kernel_size=(1, 1), stride=(2, 2), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 512, 28, 28], "output_shape": [1, 512, 28, 28], "num_parameters": 1024, "trainable_parameters": 1024, "has_bias": false, "module_str": "BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 512, 28, 28], "output_shape": [1, 512, 28, 28], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 512, 28, 28], "output_shape": [1, 128, 28, 28], "num_parameters": 65536, "trainable_parameters": 65536, "has_bias": false, "module_str": "Conv2d(512, 128, kernel_size=(1, 1), stride=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 128, 28, 28], "output_shape": [1, 128, 28, 28], "num_parameters": 256, "trainable_parameters": 256, "has_bias": false, "module_str": "BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 128, 28, 28], "output_shape": [1, 128, 28, 28], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 128, 28, 28], "output_shape": [1, 128, 28, 28], "num_parameters": 147456, "trainable_parameters": 147456, "has_bias": false, "module_str": "Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 128, 28, 28], "output_shape": [1, 128, 28, 28], "num_parameters": 256, "trainable_parameters": 256, "has_bias": false, "module_str": "BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 128, 28, 28], "output_shape": [1, 128, 28, 28], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 128, 28, 28], "output_shape": [1, 512, 28, 28], "num_parameters": 65536, "trainable_parameters": 65536, "has_bias": false, "module_str": "Conv2d(128, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 512, 28, 28], "output_shape": [1, 512, 28, 28], "num_parameters": 1024, "trainable_parameters": 1024, "has_bias": false, "module_str": "BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 512, 28, 28], "output_shape": [1, 512, 28, 28], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 512, 28, 28], "output_shape": [1, 128, 28, 28], "num_parameters": 65536, "trainable_parameters": 65536, "has_bias": false, "module_str": "Conv2d(512, 128, kernel_size=(1, 1), stride=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 128, 28, 28], "output_shape": [1, 128, 28, 28], "num_parameters": 256, "trainable_parameters": 256, "has_bias": false, "module_str": "BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 128, 28, 28], "output_shape": [1, 128, 28, 28], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 128, 28, 28], "output_shape": [1, 128, 28, 28], "num_parameters": 147456, "trainable_parameters": 147456, "has_bias": false, "module_str": "Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 128, 28, 28], "output_shape": [1, 128, 28, 28], "num_parameters": 256, "trainable_parameters": 256, "has_bias": false, "module_str": "BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 128, 28, 28], "output_shape": [1, 128, 28, 28], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 128, 28, 28], "output_shape": [1, 512, 28, 28], "num_parameters": 65536, "trainable_parameters": 65536, "has_bias": false, "module_str": "Conv2d(128, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 512, 28, 28], "output_shape": [1, 512, 28, 28], "num_parameters": 1024, "trainable_parameters": 1024, "has_bias": false, "module_str": "BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 512, 28, 28], "output_shape": [1, 512, 28, 28], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 512, 28, 28], "output_shape": [1, 128, 28, 28], "num_parameters": 65536, "trainable_parameters": 65536, "has_bias": false, "module_str": "Conv2d(512, 128, kernel_size=(1, 1), stride=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 128, 28, 28], "output_shape": [1, 128, 28, 28], "num_parameters": 256, "trainable_parameters": 256, "has_bias": false, "module_str": "BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 128, 28, 28], "output_shape": [1, 128, 28, 28], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 128, 28, 28], "output_shape": [1, 128, 28, 28], "num_parameters": 147456, "trainable_parameters": 147456, "has_bias": false, "module_str": "Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 128, 28, 28], "output_shape": [1, 128, 28, 28], "num_parameters": 256, "trainable_parameters": 256, "has_bias": false, "module_str": "BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 128, 28, 28], "output_shape": [1, 128, 28, 28], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 128, 28, 28], "output_shape": [1, 512, 28, 28], "num_parameters": 65536, "trainable_parameters": 65536, "has_bias": false, "module_str": "Conv2d(128, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 512, 28, 28], "output_shape": [1, 512, 28, 28], "num_parameters": 1024, "trainable_parameters": 1024, "has_bias": false, "module_str": "BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 512, 28, 28], "output_shape": [1, 512, 28, 28], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 512, 28, 28], "output_shape": [1, 256, 28, 28], "num_parameters": 131072, "trainable_parameters": 131072, "has_bias": false, "module_str": "Conv2d(512, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 256, 28, 28], "output_shape": [1, 256, 28, 28], "num_parameters": 512, "trainable_parameters": 512, "has_bias": false, "module_str": "BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 256, 28, 28], "output_shape": [1, 256, 28, 28], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 256, 28, 28], "output_shape": [1, 256, 14, 14], "num_parameters": 589824, "trainable_parameters": 589824, "has_bias": false, "module_str": "Conv2d(256, 256, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 256, 14, 14], "output_shape": [1, 256, 14, 14], "num_parameters": 512, "trainable_parameters": 512, "has_bias": false, "module_str": "BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 256, 14, 14], "output_shape": [1, 256, 14, 14], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 256, 14, 14], "output_shape": [1, 1024, 14, 14], "num_parameters": 262144, "trainable_parameters": 262144, "has_bias": false, "module_str": "Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 1024, 14, 14], "output_shape": [1, 1024, 14, 14], "num_parameters": 2048, "trainable_parameters": 2048, "has_bias": false, "module_str": "BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 512, 28, 28], "output_shape": [1, 1024, 14, 14], "num_parameters": 524288, "trainable_parameters": 524288, "has_bias": false, "module_str": "Conv2d(512, 1024, kernel_size=(1, 1), stride=(2, 2), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 1024, 14, 14], "output_shape": [1, 1024, 14, 14], "num_parameters": 2048, "trainable_parameters": 2048, "has_bias": false, "module_str": "BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 1024, 14, 14], "output_shape": [1, 1024, 14, 14], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 1024, 14, 14], "output_shape": [1, 256, 14, 14], "num_parameters": 262144, "trainable_parameters": 262144, "has_bias": false, "module_str": "Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 256, 14, 14], "output_shape": [1, 256, 14, 14], "num_parameters": 512, "trainable_parameters": 512, "has_bias": false, "module_str": "BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 256, 14, 14], "output_shape": [1, 256, 14, 14], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 256, 14, 14], "output_shape": [1, 256, 14, 14], "num_parameters": 589824, "trainable_parameters": 589824, "has_bias": false, "module_str": "Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 256, 14, 14], "output_shape": [1, 256, 14, 14], "num_parameters": 512, "trainable_parameters": 512, "has_bias": false, "module_str": "BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 256, 14, 14], "output_shape": [1, 256, 14, 14], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 256, 14, 14], "output_shape": [1, 1024, 14, 14], "num_parameters": 262144, "trainable_parameters": 262144, "has_bias": false, "module_str": "Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 1024, 14, 14], "output_shape": [1, 1024, 14, 14], "num_parameters": 2048, "trainable_parameters": 2048, "has_bias": false, "module_str": "BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 1024, 14, 14], "output_shape": [1, 1024, 14, 14], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 1024, 14, 14], "output_shape": [1, 256, 14, 14], "num_parameters": 262144, "trainable_parameters": 262144, "has_bias": false, "module_str": "Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 256, 14, 14], "output_shape": [1, 256, 14, 14], "num_parameters": 512, "trainable_parameters": 512, "has_bias": false, "module_str": "BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 256, 14, 14], "output_shape": [1, 256, 14, 14], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 256, 14, 14], "output_shape": [1, 256, 14, 14], "num_parameters": 589824, "trainable_parameters": 589824, "has_bias": false, "module_str": "Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 256, 14, 14], "output_shape": [1, 256, 14, 14], "num_parameters": 512, "trainable_parameters": 512, "has_bias": false, "module_str": "BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 256, 14, 14], "output_shape": [1, 256, 14, 14], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 256, 14, 14], "output_shape": [1, 1024, 14, 14], "num_parameters": 262144, "trainable_parameters": 262144, "has_bias": false, "module_str": "Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 1024, 14, 14], "output_shape": [1, 1024, 14, 14], "num_parameters": 2048, "trainable_parameters": 2048, "has_bias": false, "module_str": "BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 1024, 14, 14], "output_shape": [1, 1024, 14, 14], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 1024, 14, 14], "output_shape": [1, 256, 14, 14], "num_parameters": 262144, "trainable_parameters": 262144, "has_bias": false, "module_str": "Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 256, 14, 14], "output_shape": [1, 256, 14, 14], "num_parameters": 512, "trainable_parameters": 512, "has_bias": false, "module_str": "BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 256, 14, 14], "output_shape": [1, 256, 14, 14], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 256, 14, 14], "output_shape": [1, 256, 14, 14], "num_parameters": 589824, "trainable_parameters": 589824, "has_bias": false, "module_str": "Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 256, 14, 14], "output_shape": [1, 256, 14, 14], "num_parameters": 512, "trainable_parameters": 512, "has_bias": false, "module_str": "BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 256, 14, 14], "output_shape": [1, 256, 14, 14], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 256, 14, 14], "output_shape": [1, 1024, 14, 14], "num_parameters": 262144, "trainable_parameters": 262144, "has_bias": false, "module_str": "Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 1024, 14, 14], "output_shape": [1, 1024, 14, 14], "num_parameters": 2048, "trainable_parameters": 2048, "has_bias": false, "module_str": "BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 1024, 14, 14], "output_shape": [1, 1024, 14, 14], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 1024, 14, 14], "output_shape": [1, 256, 14, 14], "num_parameters": 262144, "trainable_parameters": 262144, "has_bias": false, "module_str": "Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 256, 14, 14], "output_shape": [1, 256, 14, 14], "num_parameters": 512, "trainable_parameters": 512, "has_bias": false, "module_str": "BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 256, 14, 14], "output_shape": [1, 256, 14, 14], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 256, 14, 14], "output_shape": [1, 256, 14, 14], "num_parameters": 589824, "trainable_parameters": 589824, "has_bias": false, "module_str": "Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 256, 14, 14], "output_shape": [1, 256, 14, 14], "num_parameters": 512, "trainable_parameters": 512, "has_bias": false, "module_str": "BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 256, 14, 14], "output_shape": [1, 256, 14, 14], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 256, 14, 14], "output_shape": [1, 1024, 14, 14], "num_parameters": 262144, "trainable_parameters": 262144, "has_bias": false, "module_str": "Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 1024, 14, 14], "output_shape": [1, 1024, 14, 14], "num_parameters": 2048, "trainable_parameters": 2048, "has_bias": false, "module_str": "BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 1024, 14, 14], "output_shape": [1, 1024, 14, 14], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 1024, 14, 14], "output_shape": [1, 256, 14, 14], "num_parameters": 262144, "trainable_parameters": 262144, "has_bias": false, "module_str": "Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 256, 14, 14], "output_shape": [1, 256, 14, 14], "num_parameters": 512, "trainable_parameters": 512, "has_bias": false, "module_str": "BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 256, 14, 14], "output_shape": [1, 256, 14, 14], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 256, 14, 14], "output_shape": [1, 256, 14, 14], "num_parameters": 589824, "trainable_parameters": 589824, "has_bias": false, "module_str": "Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 256, 14, 14], "output_shape": [1, 256, 14, 14], "num_parameters": 512, "trainable_parameters": 512, "has_bias": false, "module_str": "BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 256, 14, 14], "output_shape": [1, 256, 14, 14], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 256, 14, 14], "output_shape": [1, 1024, 14, 14], "num_parameters": 262144, "trainable_parameters": 262144, "has_bias": false, "module_str": "Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 1024, 14, 14], "output_shape": [1, 1024, 14, 14], "num_parameters": 2048, "trainable_parameters": 2048, "has_bias": false, "module_str": "BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 1024, 14, 14], "output_shape": [1, 1024, 14, 14], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 1024, 14, 14], "output_shape": [1, 512, 14, 14], "num_parameters": 524288, "trainable_parameters": 524288, "has_bias": false, "module_str": "Conv2d(1024, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 512, 14, 14], "output_shape": [1, 512, 14, 14], "num_parameters": 1024, "trainable_parameters": 1024, "has_bias": false, "module_str": "BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 512, 14, 14], "output_shape": [1, 512, 14, 14], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 512, 14, 14], "output_shape": [1, 512, 7, 7], "num_parameters": 2359296, "trainable_parameters": 2359296, "has_bias": false, "module_str": "Conv2d(512, 512, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 512, 7, 7], "output_shape": [1, 512, 7, 7], "num_parameters": 1024, "trainable_parameters": 1024, "has_bias": false, "module_str": "BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 512, 7, 7], "output_shape": [1, 512, 7, 7], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 512, 7, 7], "output_shape": [1, 2048, 7, 7], "num_parameters": 1048576, "trainable_parameters": 1048576, "has_bias": false, "module_str": "Conv2d(512, 2048, kernel_size=(1, 1), stride=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 2048, 7, 7], "output_shape": [1, 2048, 7, 7], "num_parameters": 4096, "trainable_parameters": 4096, "has_bias": false, "module_str": "BatchNorm2d(2048, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 1024, 14, 14], "output_shape": [1, 2048, 7, 7], "num_parameters": 2097152, "trainable_parameters": 2097152, "has_bias": false, "module_str": "Conv2d(1024, 2048, kernel_size=(1, 1), stride=(2, 2), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 2048, 7, 7], "output_shape": [1, 2048, 7, 7], "num_parameters": 4096, "trainable_parameters": 4096, "has_bias": false, "module_str": "BatchNorm2d(2048, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 2048, 7, 7], "output_shape": [1, 2048, 7, 7], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 2048, 7, 7], "output_shape": [1, 512, 7, 7], "num_parameters": 1048576, "trainable_parameters": 1048576, "has_bias": false, "module_str": "Conv2d(2048, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 512, 7, 7], "output_shape": [1, 512, 7, 7], "num_parameters": 1024, "trainable_parameters": 1024, "has_bias": false, "module_str": "BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 512, 7, 7], "output_shape": [1, 512, 7, 7], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 512, 7, 7], "output_shape": [1, 512, 7, 7], "num_parameters": 2359296, "trainable_parameters": 2359296, "has_bias": false, "module_str": "Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 512, 7, 7], "output_shape": [1, 512, 7, 7], "num_parameters": 1024, "trainable_parameters": 1024, "has_bias": false, "module_str": "BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 512, 7, 7], "output_shape": [1, 512, 7, 7], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 512, 7, 7], "output_shape": [1, 2048, 7, 7], "num_parameters": 1048576, "trainable_parameters": 1048576, "has_bias": false, "module_str": "Conv2d(512, 2048, kernel_size=(1, 1), stride=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 2048, 7, 7], "output_shape": [1, 2048, 7, 7], "num_parameters": 4096, "trainable_parameters": 4096, "has_bias": false, "module_str": "BatchNorm2d(2048, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 2048, 7, 7], "output_shape": [1, 2048, 7, 7], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 2048, 7, 7], "output_shape": [1, 512, 7, 7], "num_parameters": 1048576, "trainable_parameters": 1048576, "has_bias": false, "module_str": "Conv2d(2048, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 512, 7, 7], "output_shape": [1, 512, 7, 7], "num_parameters": 1024, "trainable_parameters": 1024, "has_bias": false, "module_str": "BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 512, 7, 7], "output_shape": [1, 512, 7, 7], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 512, 7, 7], "output_shape": [1, 512, 7, 7], "num_parameters": 2359296, "trainable_parameters": 2359296, "has_bias": false, "module_str": "Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 512, 7, 7], "output_shape": [1, 512, 7, 7], "num_parameters": 1024, "trainable_parameters": 1024, "has_bias": false, "module_str": "BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 512, 7, 7], "output_shape": [1, 512, 7, 7], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "Conv2d", "layer_type": "Conv2d", "input_shape": [1, 512, 7, 7], "output_shape": [1, 2048, 7, 7], "num_parameters": 1048576, "trainable_parameters": 1048576, "has_bias": false, "module_str": "Conv2d(512, 2048, kernel_size=(1, 1), stride=(1, 1), bias=False)"}, {"layer_name": "BatchNorm2d", "layer_type": "BatchNorm2d", "input_shape": [1, 2048, 7, 7], "output_shape": [1, 2048, 7, 7], "num_parameters": 4096, "trainable_parameters": 4096, "has_bias": false, "module_str": "BatchNorm2d(2048, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"}, {"layer_name": "ReLU", "layer_type": "ReLU", "input_shape": [1, 2048, 7, 7], "output_shape": [1, 2048, 7, 7], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "ReLU(inplace=True)"}, {"layer_name": "AdaptiveAvgPool2d", "layer_type": "AdaptiveAvgPool2d", "input_shape": [1, 2048, 7, 7], "output_shape": [1, 2048, 1, 1], "num_parameters": 0, "trainable_parameters": 0, "has_bias": false, "module_str": "AdaptiveAvgPool2d(output_size=(1, 1))"}, {"layer_name": "Linear", "layer_type": "Linear", "input_shape": [1, 2048], "output_shape": [1, 100], "num_parameters": 204900, "trainable_parameters": 204900, "has_bias": false, "module_str": "Linear(in_features=2048, out_features=100, bias=True)"}], "flops": {"total_flops": 4131899392.0, "total_flops_formatted": "4.132G", "total_params_from_flop_analysis": 23712932.0, "total_params_formatted": "23.713M", "flops_per_parameter": 174.2466681049817, "input_size": [1, 3, 224, 224]}, "memory": {"parameter_memory_bytes": 94851728, "parameter_memory_mb": 90.45765686035156, "buffer_memory_bytes": 213176, "buffer_memory_mb": 0.20330047607421875, "activation_memory_bytes": 128158096, "activation_memory_mb": 122.22108459472656, "total_memory_bytes": 223223000, "total_memory_mb": 212.88204193115234, "input_size": [1, 3, 224, 224]}, "analysis_metadata": {"pytorch_version": "2.4.1+cpu", "total_layers_analyzed": 158}}