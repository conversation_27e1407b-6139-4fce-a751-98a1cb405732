layer_name,layer_type,input_shape,output_shape,num_parameters,trainable_parameters,has_bias,module_str
Conv2d,Conv2d,"[1, 3, 224, 224]","[1, 64, 112, 112]",9408.0,9408.0,False,"Conv2d(3, 64, kernel_size=(7, 7), stride=(2, 2), padding=(3, 3), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 64, 112, 112]","[1, 64, 112, 112]",128.0,128.0,False,"BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 64, 112, 112]","[1, 64, 112, 112]",0.0,0.0,False,ReLU(inplace=True)
MaxPool2d,MaxPool2d,"[1, 64, 112, 112]","[1, 64, 56, 56]",0.0,0.0,False,"MaxPool2d(kernel_size=3, stride=2, padding=1, dilation=1, ceil_mode=False)"
Conv2d,Conv2d,"[1, 64, 56, 56]","[1, 64, 56, 56]",4096.0,4096.0,False,"Conv2d(64, 64, kernel_size=(1, 1), stride=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 64, 56, 56]","[1, 64, 56, 56]",128.0,128.0,False,"BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 64, 56, 56]","[1, 64, 56, 56]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 64, 56, 56]","[1, 64, 56, 56]",36864.0,36864.0,False,"Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 64, 56, 56]","[1, 64, 56, 56]",128.0,128.0,False,"BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 64, 56, 56]","[1, 64, 56, 56]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 64, 56, 56]","[1, 256, 56, 56]",16384.0,16384.0,False,"Conv2d(64, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 256, 56, 56]","[1, 256, 56, 56]",512.0,512.0,False,"BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
Conv2d,Conv2d,"[1, 64, 56, 56]","[1, 256, 56, 56]",16384.0,16384.0,False,"Conv2d(64, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 256, 56, 56]","[1, 256, 56, 56]",512.0,512.0,False,"BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 256, 56, 56]","[1, 256, 56, 56]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 256, 56, 56]","[1, 64, 56, 56]",16384.0,16384.0,False,"Conv2d(256, 64, kernel_size=(1, 1), stride=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 64, 56, 56]","[1, 64, 56, 56]",128.0,128.0,False,"BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 64, 56, 56]","[1, 64, 56, 56]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 64, 56, 56]","[1, 64, 56, 56]",36864.0,36864.0,False,"Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 64, 56, 56]","[1, 64, 56, 56]",128.0,128.0,False,"BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 64, 56, 56]","[1, 64, 56, 56]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 64, 56, 56]","[1, 256, 56, 56]",16384.0,16384.0,False,"Conv2d(64, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 256, 56, 56]","[1, 256, 56, 56]",512.0,512.0,False,"BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 256, 56, 56]","[1, 256, 56, 56]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 256, 56, 56]","[1, 64, 56, 56]",16384.0,16384.0,False,"Conv2d(256, 64, kernel_size=(1, 1), stride=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 64, 56, 56]","[1, 64, 56, 56]",128.0,128.0,False,"BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 64, 56, 56]","[1, 64, 56, 56]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 64, 56, 56]","[1, 64, 56, 56]",36864.0,36864.0,False,"Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 64, 56, 56]","[1, 64, 56, 56]",128.0,128.0,False,"BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 64, 56, 56]","[1, 64, 56, 56]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 64, 56, 56]","[1, 256, 56, 56]",16384.0,16384.0,False,"Conv2d(64, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 256, 56, 56]","[1, 256, 56, 56]",512.0,512.0,False,"BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 256, 56, 56]","[1, 256, 56, 56]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 256, 56, 56]","[1, 128, 56, 56]",32768.0,32768.0,False,"Conv2d(256, 128, kernel_size=(1, 1), stride=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 128, 56, 56]","[1, 128, 56, 56]",256.0,256.0,False,"BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 128, 56, 56]","[1, 128, 56, 56]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 128, 56, 56]","[1, 128, 28, 28]",147456.0,147456.0,False,"Conv2d(128, 128, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 128, 28, 28]","[1, 128, 28, 28]",256.0,256.0,False,"BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 128, 28, 28]","[1, 128, 28, 28]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 128, 28, 28]","[1, 512, 28, 28]",65536.0,65536.0,False,"Conv2d(128, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 512, 28, 28]","[1, 512, 28, 28]",1024.0,1024.0,False,"BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
Conv2d,Conv2d,"[1, 256, 56, 56]","[1, 512, 28, 28]",131072.0,131072.0,False,"Conv2d(256, 512, kernel_size=(1, 1), stride=(2, 2), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 512, 28, 28]","[1, 512, 28, 28]",1024.0,1024.0,False,"BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 512, 28, 28]","[1, 512, 28, 28]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 512, 28, 28]","[1, 128, 28, 28]",65536.0,65536.0,False,"Conv2d(512, 128, kernel_size=(1, 1), stride=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 128, 28, 28]","[1, 128, 28, 28]",256.0,256.0,False,"BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 128, 28, 28]","[1, 128, 28, 28]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 128, 28, 28]","[1, 128, 28, 28]",147456.0,147456.0,False,"Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 128, 28, 28]","[1, 128, 28, 28]",256.0,256.0,False,"BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 128, 28, 28]","[1, 128, 28, 28]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 128, 28, 28]","[1, 512, 28, 28]",65536.0,65536.0,False,"Conv2d(128, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 512, 28, 28]","[1, 512, 28, 28]",1024.0,1024.0,False,"BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 512, 28, 28]","[1, 512, 28, 28]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 512, 28, 28]","[1, 128, 28, 28]",65536.0,65536.0,False,"Conv2d(512, 128, kernel_size=(1, 1), stride=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 128, 28, 28]","[1, 128, 28, 28]",256.0,256.0,False,"BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 128, 28, 28]","[1, 128, 28, 28]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 128, 28, 28]","[1, 128, 28, 28]",147456.0,147456.0,False,"Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 128, 28, 28]","[1, 128, 28, 28]",256.0,256.0,False,"BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 128, 28, 28]","[1, 128, 28, 28]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 128, 28, 28]","[1, 512, 28, 28]",65536.0,65536.0,False,"Conv2d(128, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 512, 28, 28]","[1, 512, 28, 28]",1024.0,1024.0,False,"BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 512, 28, 28]","[1, 512, 28, 28]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 512, 28, 28]","[1, 128, 28, 28]",65536.0,65536.0,False,"Conv2d(512, 128, kernel_size=(1, 1), stride=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 128, 28, 28]","[1, 128, 28, 28]",256.0,256.0,False,"BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 128, 28, 28]","[1, 128, 28, 28]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 128, 28, 28]","[1, 128, 28, 28]",147456.0,147456.0,False,"Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 128, 28, 28]","[1, 128, 28, 28]",256.0,256.0,False,"BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 128, 28, 28]","[1, 128, 28, 28]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 128, 28, 28]","[1, 512, 28, 28]",65536.0,65536.0,False,"Conv2d(128, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 512, 28, 28]","[1, 512, 28, 28]",1024.0,1024.0,False,"BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 512, 28, 28]","[1, 512, 28, 28]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 512, 28, 28]","[1, 256, 28, 28]",131072.0,131072.0,False,"Conv2d(512, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 256, 28, 28]","[1, 256, 28, 28]",512.0,512.0,False,"BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 256, 28, 28]","[1, 256, 28, 28]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 256, 28, 28]","[1, 256, 14, 14]",589824.0,589824.0,False,"Conv2d(256, 256, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 256, 14, 14]","[1, 256, 14, 14]",512.0,512.0,False,"BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 256, 14, 14]","[1, 256, 14, 14]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 256, 14, 14]","[1, 1024, 14, 14]",262144.0,262144.0,False,"Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 1024, 14, 14]","[1, 1024, 14, 14]",2048.0,2048.0,False,"BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
Conv2d,Conv2d,"[1, 512, 28, 28]","[1, 1024, 14, 14]",524288.0,524288.0,False,"Conv2d(512, 1024, kernel_size=(1, 1), stride=(2, 2), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 1024, 14, 14]","[1, 1024, 14, 14]",2048.0,2048.0,False,"BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 1024, 14, 14]","[1, 1024, 14, 14]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 1024, 14, 14]","[1, 256, 14, 14]",262144.0,262144.0,False,"Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 256, 14, 14]","[1, 256, 14, 14]",512.0,512.0,False,"BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 256, 14, 14]","[1, 256, 14, 14]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 256, 14, 14]","[1, 256, 14, 14]",589824.0,589824.0,False,"Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 256, 14, 14]","[1, 256, 14, 14]",512.0,512.0,False,"BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 256, 14, 14]","[1, 256, 14, 14]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 256, 14, 14]","[1, 1024, 14, 14]",262144.0,262144.0,False,"Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 1024, 14, 14]","[1, 1024, 14, 14]",2048.0,2048.0,False,"BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 1024, 14, 14]","[1, 1024, 14, 14]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 1024, 14, 14]","[1, 256, 14, 14]",262144.0,262144.0,False,"Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 256, 14, 14]","[1, 256, 14, 14]",512.0,512.0,False,"BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 256, 14, 14]","[1, 256, 14, 14]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 256, 14, 14]","[1, 256, 14, 14]",589824.0,589824.0,False,"Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 256, 14, 14]","[1, 256, 14, 14]",512.0,512.0,False,"BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 256, 14, 14]","[1, 256, 14, 14]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 256, 14, 14]","[1, 1024, 14, 14]",262144.0,262144.0,False,"Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 1024, 14, 14]","[1, 1024, 14, 14]",2048.0,2048.0,False,"BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 1024, 14, 14]","[1, 1024, 14, 14]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 1024, 14, 14]","[1, 256, 14, 14]",262144.0,262144.0,False,"Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 256, 14, 14]","[1, 256, 14, 14]",512.0,512.0,False,"BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 256, 14, 14]","[1, 256, 14, 14]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 256, 14, 14]","[1, 256, 14, 14]",589824.0,589824.0,False,"Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 256, 14, 14]","[1, 256, 14, 14]",512.0,512.0,False,"BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 256, 14, 14]","[1, 256, 14, 14]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 256, 14, 14]","[1, 1024, 14, 14]",262144.0,262144.0,False,"Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 1024, 14, 14]","[1, 1024, 14, 14]",2048.0,2048.0,False,"BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 1024, 14, 14]","[1, 1024, 14, 14]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 1024, 14, 14]","[1, 256, 14, 14]",262144.0,262144.0,False,"Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 256, 14, 14]","[1, 256, 14, 14]",512.0,512.0,False,"BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 256, 14, 14]","[1, 256, 14, 14]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 256, 14, 14]","[1, 256, 14, 14]",589824.0,589824.0,False,"Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 256, 14, 14]","[1, 256, 14, 14]",512.0,512.0,False,"BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 256, 14, 14]","[1, 256, 14, 14]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 256, 14, 14]","[1, 1024, 14, 14]",262144.0,262144.0,False,"Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 1024, 14, 14]","[1, 1024, 14, 14]",2048.0,2048.0,False,"BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 1024, 14, 14]","[1, 1024, 14, 14]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 1024, 14, 14]","[1, 256, 14, 14]",262144.0,262144.0,False,"Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 256, 14, 14]","[1, 256, 14, 14]",512.0,512.0,False,"BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 256, 14, 14]","[1, 256, 14, 14]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 256, 14, 14]","[1, 256, 14, 14]",589824.0,589824.0,False,"Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 256, 14, 14]","[1, 256, 14, 14]",512.0,512.0,False,"BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 256, 14, 14]","[1, 256, 14, 14]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 256, 14, 14]","[1, 1024, 14, 14]",262144.0,262144.0,False,"Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 1024, 14, 14]","[1, 1024, 14, 14]",2048.0,2048.0,False,"BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 1024, 14, 14]","[1, 1024, 14, 14]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 1024, 14, 14]","[1, 512, 14, 14]",524288.0,524288.0,False,"Conv2d(1024, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 512, 14, 14]","[1, 512, 14, 14]",1024.0,1024.0,False,"BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 512, 14, 14]","[1, 512, 14, 14]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 512, 14, 14]","[1, 512, 7, 7]",2359296.0,2359296.0,False,"Conv2d(512, 512, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 512, 7, 7]","[1, 512, 7, 7]",1024.0,1024.0,False,"BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 512, 7, 7]","[1, 512, 7, 7]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 512, 7, 7]","[1, 2048, 7, 7]",1048576.0,1048576.0,False,"Conv2d(512, 2048, kernel_size=(1, 1), stride=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 2048, 7, 7]","[1, 2048, 7, 7]",4096.0,4096.0,False,"BatchNorm2d(2048, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
Conv2d,Conv2d,"[1, 1024, 14, 14]","[1, 2048, 7, 7]",2097152.0,2097152.0,False,"Conv2d(1024, 2048, kernel_size=(1, 1), stride=(2, 2), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 2048, 7, 7]","[1, 2048, 7, 7]",4096.0,4096.0,False,"BatchNorm2d(2048, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 2048, 7, 7]","[1, 2048, 7, 7]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 2048, 7, 7]","[1, 512, 7, 7]",1048576.0,1048576.0,False,"Conv2d(2048, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 512, 7, 7]","[1, 512, 7, 7]",1024.0,1024.0,False,"BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 512, 7, 7]","[1, 512, 7, 7]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 512, 7, 7]","[1, 512, 7, 7]",2359296.0,2359296.0,False,"Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 512, 7, 7]","[1, 512, 7, 7]",1024.0,1024.0,False,"BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 512, 7, 7]","[1, 512, 7, 7]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 512, 7, 7]","[1, 2048, 7, 7]",1048576.0,1048576.0,False,"Conv2d(512, 2048, kernel_size=(1, 1), stride=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 2048, 7, 7]","[1, 2048, 7, 7]",4096.0,4096.0,False,"BatchNorm2d(2048, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 2048, 7, 7]","[1, 2048, 7, 7]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 2048, 7, 7]","[1, 512, 7, 7]",1048576.0,1048576.0,False,"Conv2d(2048, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 512, 7, 7]","[1, 512, 7, 7]",1024.0,1024.0,False,"BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 512, 7, 7]","[1, 512, 7, 7]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 512, 7, 7]","[1, 512, 7, 7]",2359296.0,2359296.0,False,"Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 512, 7, 7]","[1, 512, 7, 7]",1024.0,1024.0,False,"BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 512, 7, 7]","[1, 512, 7, 7]",0.0,0.0,False,ReLU(inplace=True)
Conv2d,Conv2d,"[1, 512, 7, 7]","[1, 2048, 7, 7]",1048576.0,1048576.0,False,"Conv2d(512, 2048, kernel_size=(1, 1), stride=(1, 1), bias=False)"
BatchNorm2d,BatchNorm2d,"[1, 2048, 7, 7]","[1, 2048, 7, 7]",4096.0,4096.0,False,"BatchNorm2d(2048, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)"
ReLU,ReLU,"[1, 2048, 7, 7]","[1, 2048, 7, 7]",0.0,0.0,False,ReLU(inplace=True)
AdaptiveAvgPool2d,AdaptiveAvgPool2d,"[1, 2048, 7, 7]","[1, 2048, 1, 1]",0.0,0.0,False,"AdaptiveAvgPool2d(output_size=(1, 1))"
Linear,Linear,"[1, 2048]","[1, 100]",204900.0,204900.0,False,"Linear(in_features=2048, out_features=100, bias=True)"
SUMMARY,Total Parameters,,,23712932.0,,,
SUMMARY,Trainable Parameters,,,23712932.0,,,
SUMMARY,Total FLOPs,,,4131899392.0,,,
SUMMARY,Total Memory (MB),,,212.88204193115234,,,
